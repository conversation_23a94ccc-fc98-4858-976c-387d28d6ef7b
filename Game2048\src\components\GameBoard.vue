<template>
  <div class="game-container">
    <!-- 主游戏区域 -->
    <div class="main-game-area">
      <div class="score-container">
        <div class="score-box">
          <div class="score-label">分数</div>
          <div class="score-value" ref="scoreValue">{{ score }}</div>
        </div>
        <div class="score-box">
          <div class="score-label">最高分</div>
          <div class="score-value">{{ bestScore }}</div>
        </div>
      </div>

    <div class="game-board"
         ref="gameBoard"
         tabindex="0"
         role="grid"
         aria-label="2048游戏板，使用方向键移动方块"
         @touchstart="handleTouchStart"
         @touchmove="handleTouchMove"
         @touchend="handleTouchEnd"
         @touchcancel="handleTouchCancel"
         @keydown="handleKeydown">
      <div class="grid-container">
        <div class="grid-row" v-for="row in 4" :key="`row-${row}`">
          <div
            v-for="col in 4"
            :key="`cell-${row}-${col}`"
            class="grid-cell"
          ></div>
        </div>
      </div>
      
      <TransitionGroup name="tile" tag="div" class="tile-container">
        <div
          v-for="tile in tiles"
          :key="tile.id"
          :data-id="tile.id"
          :class="['tile', `tile-${tile.value}`, `tile-position-${tile.row}-${tile.col}`]"
        >
          {{ tile.value }}
        </div>
      </TransitionGroup>
    </div>
    
    <div class="game-controls">
      <div class="main-controls">
        <button @click="restartGame" class="restart-btn modern-btn" title="开始新游戏 (R)">
          <span class="btn-icon">🎮</span>
          <span class="btn-text">新游戏</span>
        </button>

        <button @click="toggleSettings" class="settings-btn modern-btn" title="游戏设置 (H)">
          <span class="btn-icon">⚙️</span>
          <span class="btn-text">设置</span>
        </button>
      </div>

      <p class="auto-save-hint">
        <span class="hint-icon">💾</span>
        游戏进度自动保存
      </p>

      <!-- 开发测试按钮 -->
      <div class="test-controls" v-if="isDevelopment">
        <h4>🎨 动态颜色测试</h4>
        <div class="test-buttons">
          <button @click="setTestScore(500)" class="test-btn">500分</button>
          <button @click="setTestScore(2000)" class="test-btn">2K分</button>
          <button @click="setTestScore(8000)" class="test-btn">8K分</button>
          <button @click="setTestScore(15000)" class="test-btn">15K分</button>
          <button @click="setTestScore(30000)" class="test-btn">30K分</button>
          <button @click="runColorTest" class="test-btn-auto">自动测试</button>
        </div>
      </div>
    </div>
    </div>

    <!-- 右侧栏 -->
    <div class="sidebar-right">
      <!-- 键盘控制提示 -->
      <div class="control-hints">
        <h3 class="sidebar-title">
          <span class="title-icon">⌨️</span>
          键盘控制
        </h3>
        <div class="hint-list">
          <div class="hint-item">
            <div class="key-combo">
              <span class="key">↑</span>
              <span class="key">↓</span>
              <span class="key">←</span>
              <span class="key">→</span>
            </div>
            <span class="hint-text">移动方块</span>
          </div>
          <div class="hint-item">
            <div class="key-combo">
              <span class="key">R</span>
            </div>
            <span class="hint-text">重新开始</span>
          </div>
          <div class="hint-item">
            <div class="key-combo">
              <span class="key">H</span>
            </div>
            <span class="hint-text">打开设置</span>
          </div>
          <div class="hint-item">
            <div class="key-combo">
              <span class="key">Esc</span>
            </div>
            <span class="hint-text">关闭面板</span>
          </div>
        </div>
      </div>

      <!-- 公告栏 -->
      <div class="announcement-board">
        <h3 class="sidebar-title">
          <span class="title-icon">📢</span>
          游戏提示
        </h3>
        <div class="announcement-list">
          <div class="announcement-item">
            <div class="announcement-icon">💡</div>
            <div class="announcement-content">
              <div class="announcement-title">合并技巧</div>
              <div class="announcement-text">尽量将大数字放在角落，保持一个方向的移动策略</div>
            </div>
          </div>
          <div class="announcement-item">
            <div class="announcement-icon">🎯</div>
            <div class="announcement-content">
              <div class="announcement-title">目标策略</div>
              <div class="announcement-text">专注于创建2048，不要贪心追求更高数字</div>
            </div>
          </div>
          <div class="announcement-item">
            <div class="announcement-icon">⚡</div>
            <div class="announcement-content">
              <div class="announcement-title">快速提示</div>
              <div class="announcement-text">游戏进度自动保存，随时可以继续游戏</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 游戏恢复提示 -->
    <Transition name="notification">
      <div v-if="showRestoreNotification" class="restore-notification">
        <div class="notification-content">
          <span class="notification-icon">🎮</span>
          <span class="notification-text">游戏进度已恢复</span>
        </div>
      </div>
    </Transition>

    <!-- 设置面板 -->
    <Transition name="settings">
      <div v-if="showSettings" class="settings-overlay" @click="closeSettings">
        <div class="settings-panel" @click.stop>
          <div class="settings-header">
            <h3>游戏设置</h3>
            <button @click="closeSettings" class="close-btn" title="关闭设置 (Esc)">✕</button>
          </div>

          <div class="settings-content">
            <!-- 动画设置 -->
            <div class="setting-group">
              <h4>🎬 动画效果</h4>
              <div class="setting-item">
                <label class="setting-label">
                  <input type="checkbox" v-model="animationSettings.enabled" @change="applyAnimationSettings">
                  启用动画效果
                </label>
              </div>

              <div class="setting-item" v-if="animationSettings.enabled">
                <label class="setting-label">动画质量</label>
                <select v-model="animationSettings.quality" @change="applyAnimationSettings" class="setting-select">
                  <option value="low">低质量（省电）</option>
                  <option value="medium">中等质量</option>
                  <option value="high">高质量</option>
                </select>
              </div>

              <div class="setting-item" v-if="animationSettings.enabled">
                <label class="setting-label">
                  <input type="checkbox" v-model="animationSettings.particles" @change="applyAnimationSettings">
                  粒子效果
                </label>
              </div>

              <div class="setting-item" v-if="animationSettings.enabled">
                <label class="setting-label">
                  <input type="checkbox" v-model="animationSettings.backgroundEffects" @change="applyAnimationSettings">
                  背景效果
                </label>
              </div>
            </div>

            <!-- 触控设置 -->
            <div class="setting-group">
              <h4>🎯 触控设置</h4>
              <div class="setting-item">
                <label class="setting-label">滑动灵敏度</label>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.1"
                  v-model="touchSettings.sensitivity"
                  @input="saveTouchSettings"
                  class="setting-range"
                >
                <span class="range-value">{{ (touchSettings.sensitivity * 100).toFixed(0) }}%</span>
              </div>

              <div class="setting-item">
                <label class="setting-label">
                  <input type="checkbox" v-model="touchSettings.vibrationEnabled" @change="saveTouchSettings">
                  触觉反馈
                </label>
              </div>
            </div>

            <!-- 键盘设置 -->
            <div class="setting-group">
              <h4>⌨️ 键盘设置</h4>
              <div class="setting-item">
                <label class="setting-label">
                  <input type="checkbox" v-model="keyboardNavigation.showHints">
                  显示键盘提示
                </label>
              </div>
            </div>

            <!-- 性能信息 -->
            <div class="setting-group">
              <h4>📊 性能信息</h4>
              <div class="performance-info">
                <div class="info-item">
                  <span class="info-label">设备性能:</span>
                  <span class="info-value">{{ devicePerformance }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">当前FPS:</span>
                  <span class="info-value">{{ performanceMonitor.fps }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="settings-footer">
            <button @click="resetSettings" class="reset-btn">重置设置</button>
            <button @click="closeSettings" class="confirm-btn">确定</button>
          </div>
        </div>
      </div>
    </Transition>

    <div v-if="isGameOver" class="game-over-overlay">
      <div class="game-over-message">
        <h2>游戏结束!</h2>
        <p>最终分数: {{ score }}</p>

        <div v-if="!showScoreSubmission" class="game-over-actions">
          <button @click="showScoreSubmission = true" class="submit-score-btn">提交分数</button>
          <button @click="restartGame" class="restart-btn">再来一局</button>
        </div>

        <div v-else class="score-submission">
          <input
            v-model="playerName"
            type="text"
            placeholder="输入你的昵称"
            maxlength="20"
            class="player-name-input"
            @keyup.enter="submitScore"
          />
          <div class="submission-actions">
            <button @click="submitScore" :disabled="submitting || !playerName.trim()" class="submit-btn">
              {{ submitting ? '提交中...' : '提交' }}
            </button>
            <button @click="showScoreSubmission = false" class="cancel-btn">取消</button>
          </div>
          <div v-if="submissionError" class="submission-error">{{ submissionError }}</div>
          <div v-if="submissionSuccess" class="submission-success">{{ submissionSuccessMessage }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { analytics } from '../utils/analytics'

interface Tile {
  id: number
  value: number
  row: number
  col: number
}

// 响应式数据
const board = ref<number[][]>([])
const tiles = ref<Tile[]>([])
const score = ref(0)
const bestScore = ref(0)
const isGameOver = ref(false)
const gameBoard = ref<HTMLElement>()
const scoreValue = ref<HTMLElement>()
let nextTileId = 1

// {{ AURA-X: Modify - 增强触摸事件系统，支持多点触控和精确手势识别. Approval: 寸止(ID:1678886439). }}
// 🎯 增强触摸事件相关
let touchStartX = 0
let touchStartY = 0
let touchStartTime = 0
let touchMoveCount = 0
let lastTouchTime = 0

// 触控设置
const touchSettings = ref({
  sensitivity: 0.7, // 0.1-1.0，灵敏度
  minSwipeDistance: 30, // 最小滑动距离
  maxSwipeTime: 500, // 最大滑动时间（毫秒）
  doubleTapDelay: 300, // 双击延迟
  longPressDelay: 500, // 长按延迟
  vibrationEnabled: true, // 触觉反馈
  preventAccidentalSwipes: true // 防止意外滑动
})

// 触控状态
const touchState = ref({
  isMultiTouch: false,
  isSwiping: false,
  isLongPress: false,
  lastDirection: '',
  consecutiveSwipes: 0
})

// 分数提交相关
const showScoreSubmission = ref(false)
const playerName = ref('')
const submitting = ref(false)
const submissionError = ref('')
const submissionSuccess = ref(false)
const submissionSuccessMessage = ref('分数提交成功！')

// 游戏恢复提示
const showRestoreNotification = ref(false)

// 动态颜色系统
const scoreIntensity = ref(0) // 0-1 的强度值，用于控制颜色变化

// 游戏统计
const moveCount = ref(0)
const gameStartTime = ref(Date.now())
const lastMoveDirection = ref('')
const lastScoreGain = ref(0)

// 开发模式检测
const isDevelopment = ref(import.meta.env.DEV)

// {{ AURA-X: Add - 设置面板状态管理. Approval: 寸止(ID:1678886445). }}
// 🎛️ 设置面板状态
const showSettings = ref(false)

// {{ AURA-X: Add - 性能优化和用户偏好设置. Approval: 寸止(ID:1678886435). }}
// 🎛️ 性能和动画设置
const animationSettings = ref({
  enabled: true,
  quality: 'high', // 'low', 'medium', 'high'
  particles: true,
  transitions: true,
  backgroundEffects: true
})

// 设备性能检测
const devicePerformance = ref('high') // 'low', 'medium', 'high'

// 检测设备性能
const detectDevicePerformance = () => {
  // 基于硬件并发数和用户代理检测
  const cores = navigator.hardwareConcurrency || 2
  const userAgent = navigator.userAgent.toLowerCase()

  // 检测是否为移动设备
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)

  if (isMobile) {
    if (cores <= 2) {
      devicePerformance.value = 'low'
    } else if (cores <= 4) {
      devicePerformance.value = 'medium'
    } else {
      devicePerformance.value = 'high'
    }
  } else {
    // 桌面设备通常性能较好
    devicePerformance.value = cores <= 2 ? 'medium' : 'high'
  }

  // 根据设备性能自动调整动画设置
  autoAdjustAnimationSettings()
}

// 自动调整动画设置
const autoAdjustAnimationSettings = () => {
  switch (devicePerformance.value) {
    case 'low':
      animationSettings.value = {
        enabled: true,
        quality: 'low',
        particles: false,
        transitions: true,
        backgroundEffects: false
      }
      break
    case 'medium':
      animationSettings.value = {
        enabled: true,
        quality: 'medium',
        particles: true,
        transitions: true,
        backgroundEffects: true
      }
      break
    case 'high':
      animationSettings.value = {
        enabled: true,
        quality: 'high',
        particles: true,
        transitions: true,
        backgroundEffects: true
      }
      break
  }

  // 应用设置到DOM
  applyAnimationSettings()
}

// 应用动画设置到DOM
const applyAnimationSettings = () => {
  const root = document.documentElement

  if (!animationSettings.value.enabled) {
    root.classList.add('animations-disabled')
  } else {
    root.classList.remove('animations-disabled')
  }

  root.classList.remove('quality-low', 'quality-medium', 'quality-high')
  root.classList.add(`quality-${animationSettings.value.quality}`)

  if (!animationSettings.value.particles) {
    root.classList.add('particles-disabled')
  } else {
    root.classList.remove('particles-disabled')
  }

  if (!animationSettings.value.backgroundEffects) {
    root.classList.add('background-effects-disabled')
  } else {
    root.classList.remove('background-effects-disabled')
  }
}

// {{ AURA-X: Add - WCAG AA级对比度检查工具函数. Approval: 寸止(ID:1678886413). }}
// {{ Source: context7-mcp on 'WCAG Accessibility Contrast Standards' }}
// 🎯 WCAG对比度计算工具
const calculateContrastRatio = (color1: string, color2: string): number => {
  // 将HSL颜色转换为RGB
  const hslToRgb = (hslString: string): [number, number, number] => {
    const match = hslString.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/)
    if (!match) return [0, 0, 0]

    const h = parseInt(match[1]) / 360
    const s = parseInt(match[2]) / 100
    const l = parseInt(match[3]) / 100

    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    let r, g, b
    if (s === 0) {
      r = g = b = l // achromatic
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }

    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)]
  }

  // 计算相对亮度
  const getLuminance = (rgb: [number, number, number]): number => {
    const [r, g, b] = rgb.map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    return 0.2126 * r + 0.7152 * g + 0.0722 * b
  }

  const rgb1 = hslToRgb(color1)
  const rgb2 = hslToRgb(color2)
  const lum1 = getLuminance(rgb1)
  const lum2 = getLuminance(rgb2)

  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)

  return (brightest + 0.05) / (darkest + 0.05)
}

// 🔍 验证颜色组合是否符合WCAG AA标准
const validateColorContrast = (backgroundColor: string, textColor: string, isLargeText = false): boolean => {
  const ratio = calculateContrastRatio(backgroundColor, textColor)
  const minRatio = isLargeText ? 3.0 : 4.5 // WCAG AA标准
  return ratio >= minRatio
}

// 🎨 优化后的方块颜色配置（确保WCAG AA合规）
const tileColorConfig = {
  2: { bg: 'hsl(240, 100%, 98%)', text: 'hsl(240, 80%, 20%)', gradient: 'hsl(240, 80%, 95%)' },
  4: { bg: 'hsl(240, 100%, 92%)', text: 'hsl(240, 90%, 15%)', gradient: 'hsl(240, 70%, 85%)' },
  8: { bg: 'hsl(45, 95%, 88%)', text: 'hsl(30, 100%, 15%)', gradient: 'hsl(45, 85%, 75%)' },
  16: { bg: 'hsl(25, 95%, 85%)', text: 'hsl(25, 100%, 10%)', gradient: 'hsl(25, 85%, 70%)' },
  32: { bg: 'hsl(0, 85%, 85%)', text: 'hsl(0, 100%, 10%)', gradient: 'hsl(0, 75%, 75%)' },
  64: { bg: 'hsl(270, 85%, 92%)', text: 'hsl(270, 100%, 10%)', gradient: 'hsl(270, 75%, 85%)' },
  128: { bg: 'hsl(142, 85%, 90%)', text: 'hsl(142, 100%, 10%)', gradient: 'hsl(142, 75%, 80%)' },
  256: { bg: 'hsl(210, 85%, 90%)', text: 'hsl(210, 100%, 10%)', gradient: 'hsl(210, 75%, 80%)' },
  512: { bg: 'hsl(50, 95%, 85%)', text: 'hsl(40, 100%, 10%)', gradient: 'hsl(50, 85%, 70%)' },
  1024: { bg: 'hsl(30, 95%, 85%)', text: 'hsl(30, 100%, 8%)', gradient: 'hsl(30, 85%, 70%)' },
  2048: { bg: 'hsl(320, 85%, 92%)', text: 'hsl(320, 100%, 8%)', gradient: 'hsl(320, 75%, 85%)' }
}

// 🧪 开发模式下的对比度测试函数
const testContrastRatios = () => {
  if (!isDevelopment.value) return

  console.log('🎨 WCAG AA 对比度测试结果:')
  Object.entries(tileColorConfig).forEach(([value, colors]) => {
    const ratio = calculateContrastRatio(colors.bg, colors.text)
    const isCompliant = validateColorContrast(colors.bg, colors.text, false)
    console.log(`方块 ${value}: ${ratio.toFixed(2)}:1 ${isCompliant ? '✅' : '❌'} (${isCompliant ? 'WCAG AA 合规' : '需要优化'})`)
  })
}

// {{ AURA-X: Modify - 升级动态颜色系统，增加更丰富的渐变效果和视觉层次. Approval: 寸止(ID:1678886409). }}
// {{ Source: context7-mcp on 'Modern UI Design Best Practices' }}

// 🎯 增强版分数强度计算（0-1之间，使用缓动函数）
const calculateScoreIntensity = (currentScore: number) => {
  // 分数阶段重新定义，使用更平滑的过渡：
  // 0-500: 宁静期 (0-0.15) - 深蓝到浅蓝
  // 500-2000: 觉醒期 (0.15-0.35) - 蓝到紫
  // 2000-8000: 升温期 (0.35-0.6) - 紫到粉
  // 8000-20000: 紧张期 (0.6-0.85) - 粉到橙
  // 20000+: 极限期 (0.85-1.0) - 橙到红

  let intensity = 0

  if (currentScore <= 500) {
    // 使用缓动函数让初期变化更明显
    intensity = Math.pow(currentScore / 500, 0.7) * 0.15
  } else if (currentScore <= 2000) {
    intensity = 0.15 + Math.pow((currentScore - 500) / 1500, 0.8) * 0.2
  } else if (currentScore <= 8000) {
    intensity = 0.35 + Math.pow((currentScore - 2000) / 6000, 0.9) * 0.25
  } else if (currentScore <= 20000) {
    intensity = 0.6 + Math.pow((currentScore - 8000) / 12000, 1.1) * 0.25
  } else {
    // 极限期使用对数函数，避免过快饱和
    intensity = 0.85 + Math.min(Math.log10((currentScore - 20000) / 10000 + 1) * 0.15, 0.15)
  }

  return Math.min(Math.max(intensity, 0), 1)
}

// 🌈 根据强度生成现代化动态颜色（支持多种色彩模式）
const getDynamicScoreColor = (intensity: number) => {
  // 使用更现代的色彩映射，从深蓝到鲜红的渐变
  const colorStops = [
    { pos: 0.0, hue: 240, sat: 70, light: 45 },   // 深蓝
    { pos: 0.15, hue: 220, sat: 75, light: 50 },  // 蓝
    { pos: 0.35, hue: 280, sat: 80, light: 55 },  // 紫
    { pos: 0.6, hue: 320, sat: 85, light: 60 },   // 粉红
    { pos: 0.85, hue: 25, sat: 90, light: 58 },   // 橙
    { pos: 1.0, hue: 0, sat: 95, light: 55 }      // 红
  ]

  // 找到当前强度对应的颜色区间
  let lowerStop = colorStops[0]
  let upperStop = colorStops[colorStops.length - 1]

  for (let i = 0; i < colorStops.length - 1; i++) {
    if (intensity >= colorStops[i].pos && intensity <= colorStops[i + 1].pos) {
      lowerStop = colorStops[i]
      upperStop = colorStops[i + 1]
      break
    }
  }

  // 在两个颜色点之间进行插值
  const range = upperStop.pos - lowerStop.pos
  const localIntensity = range === 0 ? 0 : (intensity - lowerStop.pos) / range

  const hue = lowerStop.hue + (upperStop.hue - lowerStop.hue) * localIntensity
  const sat = lowerStop.sat + (upperStop.sat - lowerStop.sat) * localIntensity
  const light = lowerStop.light + (upperStop.light - lowerStop.light) * localIntensity

  return `hsl(${Math.round(hue)}, ${Math.round(sat)}%, ${Math.round(light)}%)`
}

// 🎨 根据强度生成多层次背景渐变
const getDynamicBackgroundGradient = (intensity: number) => {
  if (intensity < 0.15) {
    // 宁静期：深蓝渐变，带有微妙的光效
    return `linear-gradient(135deg,
      hsl(240, 70%, 75%) 0%,
      hsl(250, 65%, 80%) 30%,
      hsl(260, 60%, 85%) 100%),
      radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)`
  } else if (intensity < 0.35) {
    // 觉醒期：蓝紫渐变，增加活力
    return `linear-gradient(135deg,
      hsl(220, 75%, 70%) 0%,
      hsl(260, 70%, 75%) 50%,
      hsl(280, 65%, 80%) 100%),
      radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%)`
  } else if (intensity < 0.6) {
    // 升温期：紫粉渐变，开始变暖
    return `linear-gradient(135deg,
      hsl(280, 80%, 65%) 0%,
      hsl(310, 75%, 70%) 30%,
      hsl(330, 70%, 75%) 70%,
      hsl(350, 65%, 80%) 100%),
      radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 70%)`
  } else if (intensity < 0.85) {
    // 紧张期：粉橙渐变，热力四射
    return `linear-gradient(135deg,
      hsl(330, 85%, 60%) 0%,
      hsl(15, 80%, 65%) 25%,
      hsl(35, 85%, 70%) 75%,
      hsl(45, 80%, 75%) 100%),
      radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.25) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 200, 100, 0.2) 0%, transparent 60%)`
  } else {
    // 极限期：红橙渐变，最高强度，多层光效
    return `linear-gradient(135deg,
      hsl(0, 95%, 55%) 0%,
      hsl(15, 90%, 60%) 20%,
      hsl(30, 85%, 65%) 40%,
      hsl(45, 80%, 70%) 60%,
      hsl(60, 75%, 75%) 80%,
      hsl(75, 70%, 80%) 100%),
      radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 40%),
      radial-gradient(circle at 70% 70%, rgba(255, 150, 50, 0.25) 0%, transparent 50%),
      conic-gradient(from 45deg at 50% 50%, transparent 0deg, rgba(255, 100, 100, 0.1) 90deg, transparent 180deg)`
  }
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

// 初始化游戏
const initGame = (forceNew = false) => {
  // 加载最高分
  loadBestScore()

  // 尝试加载保存的游戏状态
  if (!forceNew && loadGameState()) {
    // 显示游戏恢复提示
    showRestoreNotification.value = true
    setTimeout(() => {
      showRestoreNotification.value = false
    }, 3000)
    // 恢复游戏时也要更新动态颜色
    updateScoreIntensity()
    return
  }

  // 创建新游戏
  board.value = Array(4).fill(null).map(() => Array(4).fill(0))
  tiles.value = []
  score.value = 0
  isGameOver.value = false
  nextTileId = 1
  moveCount.value = 0
  gameStartTime.value = Date.now()

  // 添加两个初始方块
  addRandomTile()
  addRandomTile()

  // 初始化动态颜色
  updateScoreIntensity()

  // 保存初始状态
  saveGameState()

  // 追踪游戏开始
  analytics.trackGameStart()
}

// {{ AURA-X: Modify - 增强随机方块生成，添加出现动画效果. Approval: 寸止(ID:1678886430). }}
// 添加随机方块
const addRandomTile = () => {
  const emptyCells = []
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] === 0) {
        emptyCells.push({ row, col })
      }
    }
  }

  if (emptyCells.length > 0) {
    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)]
    const value = Math.random() < 0.9 ? 2 : 4

    board.value[randomCell.row][randomCell.col] = value
    const newTile = {
      id: nextTileId++,
      value,
      row: randomCell.row,
      col: randomCell.col
    }
    tiles.value.push(newTile)

    // 🎬 为新方块添加出现动画
    setTimeout(() => {
      triggerTileAppearAnimation(newTile.id, value)
    }, 50) // 短暂延迟确保DOM更新
  }
}

// 🎬 触发方块出现动画
const triggerTileAppearAnimation = (tileId: number, value: number) => {
  const tileElement = document.querySelector(`.tile[data-id="${tileId}"]`)

  if (tileElement) {
    // 移除可能存在的旧动画类
    tileElement.classList.remove('tile-new', 'tile-appear-default', 'tile-appear-spin', 'tile-appear-drop', 'tile-appear-burst')

    // 添加新方块标识
    tileElement.classList.add('tile-new')

    // 根据方块数值选择动画类型
    let animationType = 'tile-appear-default'

    if (value === 2) {
      // 2方块使用默认弹性动画
      animationType = 'tile-appear-default'
    } else if (value === 4) {
      // 4方块使用旋转动画
      animationType = 'tile-appear-spin'
    } else if (value >= 128) {
      // 高数值方块使用爆发动画
      animationType = 'tile-appear-burst'
    } else {
      // 随机选择动画类型
      const animations = ['tile-appear-default', 'tile-appear-spin', 'tile-appear-drop']
      animationType = animations[Math.floor(Math.random() * animations.length)]
    }

    tileElement.classList.add(animationType)

    // 动画完成后清理类名
    setTimeout(() => {
      tileElement.classList.remove('tile-new', animationType)
    }, 800) // 稍长于动画时长
  }
}

// {{ AURA-X: Modify - 增强移动逻辑，添加合并动画支持. Approval: 寸止(ID:1678886432). }}
// 移动逻辑 - 向左移动
const moveLeft = (): boolean => {
  let moved = false
  const newBoard = board.value.map(row => [...row])
  const mergedPositions: Array<{row: number, col: number, value: number}> = []

  for (let row = 0; row < 4; row++) {
    const line = newBoard[row].filter(val => val !== 0)
    const merged = []
    let i = 0
    let col = 0

    while (i < line.length) {
      if (i < line.length - 1 && line[i] === line[i + 1]) {
        // 合并相同的方块
        const mergedValue = line[i] * 2
        merged.push(mergedValue)
        score.value += mergedValue

        // 记录合并位置和数值，用于动画
        mergedPositions.push({
          row,
          col,
          value: mergedValue
        })

        i += 2
      } else {
        merged.push(line[i])
        i++
      }
      col++
    }

    // 填充剩余位置为0
    while (merged.length < 4) {
      merged.push(0)
    }

    // 检查是否有移动
    for (let col = 0; col < 4; col++) {
      if (newBoard[row][col] !== merged[col]) {
        moved = true
      }
    }

    newBoard[row] = merged
  }

  if (moved) {
    board.value = newBoard
    updateTiles()

    // 🎬 触发合并动画
    if (mergedPositions.length > 0) {
      setTimeout(() => {
        mergedPositions.forEach(pos => {
          triggerMergeAnimation(pos.row, pos.col, pos.value)
        })
      }, 100) // 短暂延迟确保DOM更新
    }

    return true
  }

  return false
}

// 旋转棋盘（用于实现其他方向的移动）
const rotateBoard = (board: number[][]): number[][] => {
  const size = board.length
  const rotated = Array(size).fill(null).map(() => Array(size).fill(0))
  
  for (let row = 0; row < size; row++) {
    for (let col = 0; col < size; col++) {
      rotated[col][size - 1 - row] = board[row][col]
    }
  }
  
  return rotated
}

// {{ AURA-X: Modify - 增强移动逻辑，添加动画效果支持. Approval: 寸止(ID:1678886427). }}
// 移动方向处理
const move = (direction: string): boolean => {
  let moved = false
  let rotations = 0

  // 🎬 添加移动动画效果
  triggerMoveAnimation(direction)

  // 根据方向确定需要旋转的次数
  switch (direction) {
    case 'left':
      rotations = 0
      break
    case 'up':
      rotations = 1
      break
    case 'right':
      rotations = 2
      break
    case 'down':
      rotations = 3
      break
  }

  // 旋转棋盘到左移位置
  let currentBoard = board.value
  for (let i = 0; i < rotations; i++) {
    currentBoard = rotateBoard(currentBoard)
  }

  board.value = currentBoard
  moved = moveLeft()

  // 旋转回原来的方向
  for (let i = 0; i < (4 - rotations) % 4; i++) {
    board.value = rotateBoard(board.value)
  }

  return moved
}

// 🎬 触发移动动画效果
const triggerMoveAnimation = (direction: string) => {
  const tiles = document.querySelectorAll('.tile')

  tiles.forEach(tile => {
    // 移除之前的移动类
    tile.classList.remove('moving', 'moving-left', 'moving-right', 'moving-up', 'moving-down')

    // 添加通用移动类和方向特定类
    tile.classList.add('moving', `moving-${direction}`)
  })

  // 动画完成后清理类名
  setTimeout(() => {
    tiles.forEach(tile => {
      tile.classList.remove('moving', 'moving-left', 'moving-right', 'moving-up', 'moving-down')
    })
  }, 300) // 与CSS动画时长匹配
}

// 更新方块显示
const updateTiles = () => {
  const newTiles: Tile[] = []

  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] !== 0) {
        newTiles.push({
          id: nextTileId++,
          value: board.value[row][col],
          row,
          col
        })
      }
    }
  }

  tiles.value = newTiles
}

// 检查游戏是否结束
const checkGameOver = (): boolean => {
  // 检查是否还有空格
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      if (board.value[row][col] === 0) {
        return false
      }
    }
  }

  // 检查是否还能合并
  for (let row = 0; row < 4; row++) {
    for (let col = 0; col < 4; col++) {
      const current = board.value[row][col]

      // 检查右边
      if (col < 3 && board.value[row][col + 1] === current) {
        return false
      }

      // 检查下面
      if (row < 3 && board.value[row + 1][col] === current) {
        return false
      }
    }
  }

  return true
}

// {{ AURA-X: Modify - 增强键盘事件处理，添加快捷键和无障碍支持. Approval: 寸止(ID:1678886443). }}
// 🎹 增强键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  // 检查是否在输入框中
  const target = event.target as HTMLElement
  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
    return
  }

  let moved = false
  let handled = true

  switch (event.key) {
    // 方向键移动
    case 'ArrowLeft':
    case 'a':
    case 'A':
      event.preventDefault()
      if (!isGameOver.value) {
        lastMoveDirection.value = 'left'
        moved = move('left')
      }
      break
    case 'ArrowUp':
    case 'w':
    case 'W':
      event.preventDefault()
      if (!isGameOver.value) {
        lastMoveDirection.value = 'up'
        moved = move('up')
      }
      break
    case 'ArrowRight':
    case 'd':
    case 'D':
      event.preventDefault()
      if (!isGameOver.value) {
        lastMoveDirection.value = 'right'
        moved = move('right')
      }
      break
    case 'ArrowDown':
    case 's':
    case 'S':
      event.preventDefault()
      if (!isGameOver.value) {
        lastMoveDirection.value = 'down'
        moved = move('down')
      }
      break

    // 快捷键
    case 'r':
    case 'R':
      event.preventDefault()
      handleRestartShortcut()
      break
    case 'n':
    case 'N':
      event.preventDefault()
      if (event.ctrlKey || event.metaKey) {
        initGame(true) // Ctrl+N 新游戏
      }
      break
    case 'u':
    case 'U':
      event.preventDefault()
      handleUndoShortcut()
      break
    case 'h':
    case 'H':
      event.preventDefault()
      showKeyboardHelp()
      break
    case '?':
      event.preventDefault()
      showKeyboardHelp()
      break
    case 'Escape':
      event.preventDefault()
      handleEscapeKey()
      break
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleEnterKey()
      break
    case 'Tab':
      // Tab键焦点管理
      handleTabNavigation(event)
      break

    // 数字键快速重启（开发模式）
    case '1':
    case '2':
    case '3':
    case '4':
    case '5':
      if (isDevelopment.value && (event.ctrlKey || event.metaKey)) {
        event.preventDefault()
        handleDebugShortcut(parseInt(event.key))
      } else {
        handled = false
      }
      break

    default:
      handled = false
  }

  if (moved) {
    moveCount.value++
    addRandomTile()
    triggerScoreAnimation()
    updateBestScore()
    updateScoreIntensity()
    saveGameState()

    // 键盘移动的触觉反馈
    provideTactileFeedback('success')

    if (checkGameOver()) {
      isGameOver.value = true
      saveGameState()
      announceToScreenReader('游戏结束')
    } else {
      // 向屏幕阅读器宣布移动结果
      announceToScreenReader(`向${getDirectionName(lastMoveDirection.value)}移动，当前分数${score.value}`)
    }
  } else if (handled && !isGameOver.value) {
    // 无法移动时的反馈
    triggerShakeAnimation()
    provideTactileFeedback('blocked')
    announceToScreenReader('无法移动')
  }
}

// 🎹 键盘快捷键处理函数
const handleRestartShortcut = () => {
  if (isGameOver.value) {
    initGame(true)
    announceToScreenReader('游戏重新开始')
  } else {
    // 游戏进行中需要确认
    if (confirm('确定要重新开始游戏吗？当前进度将丢失。')) {
      initGame(true)
      announceToScreenReader('游戏重新开始')
    }
  }
}

const handleUndoShortcut = () => {
  // 撤销功能（如果实现了历史记录）
  announceToScreenReader('撤销功能暂未实现')
  console.log('撤销功能待实现')
}

const showKeyboardHelp = () => {
  const helpText = `
键盘快捷键：
• 方向键 或 WASD：移动方块
• R：重新开始游戏
• Ctrl+N：新游戏
• U：撤销（待实现）
• H 或 ?：显示帮助
• Esc：关闭弹窗
• Enter/空格：确认操作
• Tab：切换焦点
  `
  alert(helpText)
  announceToScreenReader('键盘帮助已显示')
}

const handleEscapeKey = () => {
  // 关闭任何打开的弹窗或菜单
  if (showScoreSubmission.value) {
    showScoreSubmission.value = false
    announceToScreenReader('分数提交窗口已关闭')
  }
  // 可以添加其他弹窗的关闭逻辑
}

const handleEnterKey = () => {
  if (isGameOver.value) {
    // 游戏结束时Enter重新开始
    initGame(true)
    announceToScreenReader('游戏重新开始')
  }
}

const handleTabNavigation = (event: KeyboardEvent) => {
  // Tab键焦点管理
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )

  if (focusableElements.length === 0) return

  const currentIndex = Array.from(focusableElements).indexOf(document.activeElement as Element)

  if (event.shiftKey) {
    // Shift+Tab 向前
    const prevIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1
    ;(focusableElements[prevIndex] as HTMLElement).focus()
  } else {
    // Tab 向后
    const nextIndex = currentIndex >= focusableElements.length - 1 ? 0 : currentIndex + 1
    ;(focusableElements[nextIndex] as HTMLElement).focus()
  }

  event.preventDefault()
}

const handleDebugShortcut = (level: number) => {
  // 开发模式快捷键
  console.log(`调试快捷键 ${level} 被触发`)
  // 可以添加调试功能，如设置特定分数、生成特定方块等
}

// 🔊 屏幕阅读器支持
const announceToScreenReader = (message: string) => {
  // 创建或更新ARIA live区域
  let liveRegion = document.getElementById('game-announcements')

  if (!liveRegion) {
    liveRegion = document.createElement('div')
    liveRegion.id = 'game-announcements'
    liveRegion.setAttribute('aria-live', 'polite')
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `
    document.body.appendChild(liveRegion)
  }

  liveRegion.textContent = message
}

// 🗣️ 方向名称转换
const getDirectionName = (direction: string): string => {
  const names = {
    'left': '左',
    'right': '右',
    'up': '上',
    'down': '下'
  }
  return names[direction as keyof typeof names] || direction
}

// 🎹 初始化键盘导航
const initKeyboardNavigation = () => {
  // 添加键盘导航类到body
  document.body.classList.add('keyboard-navigation')

  // 检测键盘使用
  let isUsingKeyboard = false

  // 键盘事件监听
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' || e.key.startsWith('Arrow')) {
      isUsingKeyboard = true
      document.body.classList.add('using-keyboard')
      showKeyboardHint()
    }
  })

  // 鼠标事件监听
  document.addEventListener('mousedown', () => {
    isUsingKeyboard = false
    document.body.classList.remove('using-keyboard')
    hideKeyboardHint()
  })

  // 触摸事件监听
  document.addEventListener('touchstart', () => {
    isUsingKeyboard = false
    document.body.classList.remove('using-keyboard')
    hideKeyboardHint()
  })

  // 为游戏板设置焦点
  if (gameBoard.value) {
    gameBoard.value.setAttribute('tabindex', '0')
  }

  // 添加跳过链接
  addSkipLink()
}

// 🎹 显示键盘提示
const showKeyboardHint = () => {
  let hint = document.getElementById('keyboard-hint')

  if (!hint) {
    hint = document.createElement('div')
    hint.id = 'keyboard-hint'
    hint.className = 'keyboard-hint'
    hint.innerHTML = `
      <div>键盘控制：</div>
      <div><kbd>↑</kbd><kbd>↓</kbd><kbd>←</kbd><kbd>→</kbd> 或 <kbd>W</kbd><kbd>A</kbd><kbd>S</kbd><kbd>D</kbd> 移动</div>
      <div><kbd>R</kbd> 重新开始 | <kbd>H</kbd> 帮助 | <kbd>Esc</kbd> 关闭</div>
    `
    document.body.appendChild(hint)
  }

  setTimeout(() => {
    hint?.classList.add('show')
  }, 100)

  // 5秒后自动隐藏
  setTimeout(() => {
    hideKeyboardHint()
  }, 5000)
}

// 🎹 隐藏键盘提示
const hideKeyboardHint = () => {
  const hint = document.getElementById('keyboard-hint')
  if (hint) {
    hint.classList.remove('show')
  }
}

// 🎹 添加跳过链接
const addSkipLink = () => {
  const skipLink = document.createElement('a')
  skipLink.href = '#game-board'
  skipLink.className = 'skip-link'
  skipLink.textContent = '跳转到游戏区域'
  skipLink.addEventListener('click', (e) => {
    e.preventDefault()
    gameBoard.value?.focus()
  })

  document.body.insertBefore(skipLink, document.body.firstChild)
}

// 🎹 键盘导航状态管理
const keyboardNavigation = ref({
  enabled: true,
  showHints: true,
  focusVisible: true
})

// 🎹 切换键盘导航设置
const toggleKeyboardHints = () => {
  keyboardNavigation.value.showHints = !keyboardNavigation.value.showHints

  if (!keyboardNavigation.value.showHints) {
    hideKeyboardHint()
  }

  // 保存设置
  try {
    localStorage.setItem('game2048_keyboard_settings', JSON.stringify(keyboardNavigation.value))
  } catch (error) {
    console.warn('无法保存键盘设置:', error)
  }
}

// 🎹 加载键盘导航设置
const loadKeyboardSettings = () => {
  try {
    const saved = localStorage.getItem('game2048_keyboard_settings')
    if (saved) {
      const settings = JSON.parse(saved)
      keyboardNavigation.value = { ...keyboardNavigation.value, ...settings }
    }
  } catch (error) {
    console.warn('无法加载键盘设置:', error)
  }
}

// {{ AURA-X: Add - 设置面板控制函数. Approval: 寸止(ID:1678886445). }}
// 🎛️ 设置面板控制函数
const toggleSettings = () => {
  showSettings.value = !showSettings.value

  if (showSettings.value) {
    // 打开设置时暂停游戏（如果需要）
    announceToScreenReader('设置面板已打开')
  } else {
    announceToScreenReader('设置面板已关闭')
  }
}

const closeSettings = () => {
  showSettings.value = false
  announceToScreenReader('设置面板已关闭')
}

const resetSettings = () => {
  if (confirm('确定要重置所有设置吗？')) {
    // 重置动画设置
    animationSettings.value = {
      enabled: true,
      quality: 'high',
      particles: true,
      transitions: true,
      backgroundEffects: true
    }

    // 重置触控设置
    touchSettings.value = {
      sensitivity: 0.7,
      minSwipeDistance: 30,
      maxSwipeTime: 500,
      doubleTapDelay: 300,
      longPressDelay: 500,
      vibrationEnabled: true,
      preventAccidentalSwipes: true
    }

    // 重置键盘设置
    keyboardNavigation.value = {
      enabled: true,
      showHints: true,
      focusVisible: true
    }

    // 应用设置
    applyAnimationSettings()
    saveTouchSettings()

    // 保存设置
    saveAnimationPreferences()

    announceToScreenReader('设置已重置')
  }
}

// {{ AURA-X: Modify - 增强触摸开始事件处理. Approval: 寸止(ID:1678886440). }}
// 🎯 增强触摸事件处理
const handleTouchStart = (event: TouchEvent) => {
  event.preventDefault() // 防止页面滚动

  const now = Date.now()
  touchStartTime = now
  touchMoveCount = 0

  // 检测多点触控
  if (event.touches.length > 1) {
    touchState.value.isMultiTouch = true
    return
  }

  touchState.value.isMultiTouch = false
  touchState.value.isSwiping = false
  touchState.value.isLongPress = false

  const touch = event.touches[0]
  touchStartX = touch.clientX
  touchStartY = touch.clientY

  // 检测双击
  if (now - lastTouchTime < touchSettings.value.doubleTapDelay) {
    handleDoubleTap()
    return
  }

  lastTouchTime = now

  // 设置长按检测
  setTimeout(() => {
    if (!touchState.value.isSwiping && !touchState.value.isMultiTouch) {
      handleLongPress()
    }
  }, touchSettings.value.longPressDelay)

  // 添加触觉反馈
  if (touchSettings.value.vibrationEnabled && 'vibrate' in navigator) {
    navigator.vibrate(10) // 轻微震动
  }
}

// 🎯 触摸移动事件处理
const handleTouchMove = (event: TouchEvent) => {
  event.preventDefault() // 防止页面滚动

  if (touchState.value.isMultiTouch || event.touches.length > 1) {
    return
  }

  touchMoveCount++
  touchState.value.isSwiping = true

  // 如果移动次数过多，可能是意外触摸
  if (touchSettings.value.preventAccidentalSwipes && touchMoveCount > 10) {
    return
  }
}

// 🎯 增强触摸结束事件处理
const handleTouchEnd = (event: TouchEvent) => {
  if (isGameOver.value || touchState.value.isMultiTouch || event.changedTouches.length !== 1) {
    resetTouchState()
    return
  }

  const now = Date.now()
  const touchDuration = now - touchStartTime

  // 检查是否超过最大滑动时间
  if (touchDuration > touchSettings.value.maxSwipeTime) {
    resetTouchState()
    return
  }

  const touch = event.changedTouches[0]
  const touchEndX = touch.clientX
  const touchEndY = touch.clientY

  const deltaX = touchEndX - touchStartX
  const deltaY = touchEndY - touchStartY
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

  // 根据灵敏度调整最小滑动距离
  const adjustedMinDistance = touchSettings.value.minSwipeDistance * (2 - touchSettings.value.sensitivity)

  if (distance < adjustedMinDistance) {
    resetTouchState()
    return
  }

  // 计算滑动方向
  let direction = ''
  let moved = false

  if (Math.abs(deltaX) > Math.abs(deltaY)) {
    // 水平滑动
    direction = deltaX > 0 ? 'right' : 'left'
  } else {
    // 垂直滑动
    direction = deltaY > 0 ? 'down' : 'up'
  }

  // 检测连续相同方向滑动
  if (direction === touchState.value.lastDirection) {
    touchState.value.consecutiveSwipes++
  } else {
    touchState.value.consecutiveSwipes = 1
  }

  touchState.value.lastDirection = direction
  lastMoveDirection.value = direction

  // 执行移动
  moved = move(direction)

  if (moved) {
    moveCount.value++
    addRandomTile()
    triggerScoreAnimation()
    updateBestScore()
    updateScoreIntensity()
    saveGameState()

    // 🎮 触觉反馈
    provideTactileFeedback('success')

    // 追踪移动
    analytics.trackMove(direction, lastScoreGain.value)

    if (checkGameOver()) {
      isGameOver.value = true
      saveGameState()
      provideTactileFeedback('gameOver')

      // 追踪游戏结束
      analytics.trackGameEnd(score.value, moveCount.value, Date.now() - gameStartTime.value)
    }
  } else {
    // 无法移动时的反馈
    triggerShakeAnimation()
    provideTactileFeedback('blocked')
  }

  resetTouchState()
}

// 🎯 触摸取消事件处理
const handleTouchCancel = (event: TouchEvent) => {
  resetTouchState()
}

// 🎯 重置触摸状态
const resetTouchState = () => {
  touchState.value.isSwiping = false
  touchState.value.isLongPress = false
  touchState.value.isMultiTouch = false
  touchMoveCount = 0
}

// 🎯 双击处理
const handleDoubleTap = () => {
  // 双击重新开始游戏
  if (isGameOver.value) {
    initGame(true)
    provideTactileFeedback('restart')
  } else {
    // 游戏进行中双击显示提示
    showQuickHint()
  }
}

// 🎯 长按处理
const handleLongPress = () => {
  touchState.value.isLongPress = true

  // 长按显示设置菜单或撤销功能
  if (!isGameOver.value) {
    showTouchSettings()
    provideTactileFeedback('longPress')
  }
}

// 🎮 触觉反馈系统
const provideTactileFeedback = (type: 'success' | 'blocked' | 'gameOver' | 'restart' | 'longPress') => {
  if (!touchSettings.value.vibrationEnabled || !('vibrate' in navigator)) {
    return
  }

  switch (type) {
    case 'success':
      // 成功移动：短震动
      navigator.vibrate(15)
      break
    case 'blocked':
      // 无法移动：双震动
      navigator.vibrate([30, 50, 30])
      break
    case 'gameOver':
      // 游戏结束：长震动
      navigator.vibrate(200)
      break
    case 'restart':
      // 重新开始：三次短震动
      navigator.vibrate([50, 100, 50, 100, 50])
      break
    case 'longPress':
      // 长按：中等震动
      navigator.vibrate(100)
      break
  }
}

// 🎯 显示快速提示
const showQuickHint = () => {
  // 显示当前可用的移动方向提示
  const availableDirections = getAvailableDirections()
  if (availableDirections.length > 0) {
    // 可以在这里添加视觉提示
    console.log('可用方向:', availableDirections)
  }
}

// 🎯 获取可用移动方向
const getAvailableDirections = (): string[] => {
  const directions = ['left', 'right', 'up', 'down']
  const available = []

  for (const direction of directions) {
    // 临时测试移动是否可行
    const testBoard = board.value.map(row => [...row])
    // 这里可以添加更复杂的逻辑来检测可用方向
    available.push(direction)
  }

  return available
}

// 🎛️ 显示触控设置
const showTouchSettings = () => {
  // 这里可以显示触控设置面板
  console.log('显示触控设置')
}

// 🎛️ 触控设置函数
const updateTouchSensitivity = (sensitivity: number) => {
  touchSettings.value.sensitivity = Math.max(0.1, Math.min(1.0, sensitivity))
  saveTouchSettings()
}

const toggleVibration = () => {
  touchSettings.value.vibrationEnabled = !touchSettings.value.vibrationEnabled
  saveTouchSettings()

  // 测试震动
  if (touchSettings.value.vibrationEnabled) {
    provideTactileFeedback('success')
  }
}

const saveTouchSettings = () => {
  try {
    localStorage.setItem('game2048_touch_settings', JSON.stringify(touchSettings.value))
  } catch (error) {
    console.warn('无法保存触控设置:', error)
  }
}

const loadTouchSettings = () => {
  try {
    const saved = localStorage.getItem('game2048_touch_settings')
    if (saved) {
      const settings = JSON.parse(saved)
      touchSettings.value = { ...touchSettings.value, ...settings }
    }
  } catch (error) {
    console.warn('无法加载触控设置:', error)
  }
}

// 生成验证载荷
const generateValidationPayload = (score: number, board: number[][]) => {
  const data = `${score}-${JSON.stringify(board)}-${Date.now()}`
  // 简单的哈希函数（在实际应用中应该使用更安全的方法）
  let hash = 0
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

// 提交分数
const submitScore = async () => {
  if (!playerName.value.trim()) {
    submissionError.value = '请输入昵称'
    return
  }

  submitting.value = true
  submissionError.value = ''
  submissionSuccess.value = false

  try {
    const validationPayload = generateValidationPayload(score.value, board.value)

    const response = await fetch(`${API_BASE_URL}/scores`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        playerName: playerName.value.trim(),
        score: score.value,
        validationPayload
      })
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || '提交失败')
    }

    submissionSuccess.value = true
    submissionError.value = '' // 清除错误信息

    // 追踪分数提交成功
    analytics.trackScoreSubmission(score.value, true)

    // 根据是否是新纪录显示不同的成功消息
    if (data.isNewRecord) {
      submissionSuccessMessage.value = '🎉 新纪录！分数更新成功！'
      setTimeout(() => {
        showScoreSubmission.value = false
        submissionSuccess.value = false
      }, 4000) // 新纪录显示更长时间
    } else {
      submissionSuccessMessage.value = '分数提交成功！'
      setTimeout(() => {
        showScoreSubmission.value = false
        submissionSuccess.value = false
      }, 3000)
    }

  } catch (error) {
    let errorMessage = '网络错误'
    if (error instanceof Error) {
      errorMessage = error.message
      // 特殊处理存储空间不足的错误
      if (errorMessage.includes('服务器存储空间不足')) {
        errorMessage = '🚫 服务器存储空间不足，请稍后再试或联系管理员'
      }
    }
    submissionError.value = errorMessage

    // 追踪分数提交失败
    analytics.trackScoreSubmission(score.value, false)
  } finally {
    submitting.value = false
  }
}

// 重新开始游戏
const restartGame = () => {
  clearGameState()
  showScoreSubmission.value = false
  playerName.value = ''
  submissionError.value = ''
  submissionSuccess.value = false
  initGame(true)
}

// 测试函数
const setTestScore = (testScore: number) => {
  score.value = testScore
  updateScoreIntensity()
  triggerScoreAnimation()
  console.log(`🎨 设置测试分数: ${testScore}, 强度: ${scoreIntensity.value.toFixed(2)}`)
}

const runColorTest = () => {
  console.log('🎨 开始自动颜色测试...')
  const testScores = [0, 500, 1500, 3000, 8000, 12000, 20000, 50000]
  let index = 0

  const testInterval = setInterval(() => {
    if (index >= testScores.length) {
      clearInterval(testInterval)
      console.log('✅ 动态颜色测试完成')
      return
    }

    setTestScore(testScores[index])
    index++
  }, 2000)
}

// 保存和加载游戏状态
const saveGameState = () => {
  const gameState = {
    board: board.value,
    tiles: tiles.value,
    score: score.value,
    isGameOver: isGameOver.value,
    nextTileId
  }
  localStorage.setItem('game2048-state', JSON.stringify(gameState))
}

const loadGameState = (): boolean => {
  const saved = localStorage.getItem('game2048-state')
  if (saved) {
    try {
      const gameState = JSON.parse(saved)
      board.value = gameState.board
      tiles.value = gameState.tiles
      score.value = gameState.score
      isGameOver.value = gameState.isGameOver
      nextTileId = gameState.nextTileId
      return true
    } catch (e) {
      console.error('Failed to load game state:', e)
      return false
    }
  }
  return false
}

const clearGameState = () => {
  localStorage.removeItem('game2048-state')
}

// 保存和加载最高分
const saveBestScore = () => {
  localStorage.setItem('game2048-best-score', bestScore.value.toString())
}

const loadBestScore = () => {
  const saved = localStorage.getItem('game2048-best-score')
  if (saved) {
    bestScore.value = parseInt(saved, 10)
  }
}

const updateBestScore = () => {
  if (score.value > bestScore.value) {
    bestScore.value = score.value
    saveBestScore()
  }
}

// {{ AURA-X: Modify - 增强动态样式更新系统，添加更丰富的视觉层次. Approval: 寸止(ID:1678886410). }}
// 🎨 更新分数强度和动态样式（增强版）
const updateScoreIntensity = () => {
  scoreIntensity.value = calculateScoreIntensity(score.value)

  // 获取DOM元素
  const scoreContainer = document.querySelector('.score-container') as HTMLElement
  const gameContainer = document.querySelector('.game-container') as HTMLElement
  const gameBoard = document.querySelector('.game-board') as HTMLElement

  if (scoreContainer && gameContainer) {
    const intensity = scoreIntensity.value
    const dynamicColor = getDynamicScoreColor(intensity)
    const dynamicBackground = getDynamicBackgroundGradient(intensity)

    // 🎯 应用动态颜色到分数值和标签
    const scoreValues = scoreContainer.querySelectorAll('.score-value')
    const scoreLabels = scoreContainer.querySelectorAll('.score-label')

    scoreValues.forEach(element => {
      const el = element as HTMLElement
      el.style.color = dynamicColor
      // 添加文字阴影增强可读性
      el.style.textShadow = intensity > 0.6
        ? '0 2px 4px rgba(0, 0, 0, 0.3), 0 0 8px rgba(255, 255, 255, 0.2)'
        : '0 1px 2px rgba(0, 0, 0, 0.1)'
    })

    // 🏷️ 分数标签动态颜色和效果
    const labelColor = intensity > 0.6
      ? 'rgba(255, 255, 255, 0.95)'
      : intensity > 0.35
        ? 'rgba(255, 255, 255, 0.85)'
        : 'rgba(102, 126, 234, 0.8)'

    scoreLabels.forEach(element => {
      const el = element as HTMLElement
      el.style.color = labelColor
      el.style.textShadow = intensity > 0.6 ? '0 1px 2px rgba(0, 0, 0, 0.2)' : 'none'
    })

    // 📦 应用动态背景到分数框
    const scoreBoxes = scoreContainer.querySelectorAll('.score-box')
    scoreBoxes.forEach(element => {
      const el = element as HTMLElement
      el.style.background = dynamicBackground

      // 根据强度添加不同的边框效果
      if (intensity > 0.85) {
        el.style.border = '2px solid rgba(255, 255, 255, 0.4)'
        el.classList.add('score-pulse-intense')
        el.classList.remove('score-pulse-medium', 'score-pulse-light')
      } else if (intensity > 0.6) {
        el.style.border = '1px solid rgba(255, 255, 255, 0.3)'
        el.classList.add('score-pulse-medium')
        el.classList.remove('score-pulse-intense', 'score-pulse-light')
      } else if (intensity > 0.35) {
        el.style.border = '1px solid rgba(255, 255, 255, 0.2)'
        el.classList.add('score-pulse-light')
        el.classList.remove('score-pulse-intense', 'score-pulse-medium')
      } else {
        el.style.border = '1px solid rgba(255, 255, 255, 0.2)'
        el.classList.remove('score-pulse-intense', 'score-pulse-medium', 'score-pulse-light')
      }
    })

    // 🎮 为整个游戏容器添加动态背景氛围
    gameContainer.classList.remove('game-calm', 'game-warm', 'game-heated', 'game-intense', 'game-extreme')

    if (intensity > 0.85) {
      gameContainer.classList.add('game-extreme')
    } else if (intensity > 0.6) {
      gameContainer.classList.add('game-intense')
    } else if (intensity > 0.35) {
      gameContainer.classList.add('game-heated')
    } else if (intensity > 0.15) {
      gameContainer.classList.add('game-warm')
    } else {
      gameContainer.classList.add('game-calm')
    }

    // 🎲 为游戏板添加动态效果
    if (gameBoard) {
      if (intensity > 0.85) {
        gameBoard.style.boxShadow = 'var(--shadow-2xl), 0 0 40px rgba(255, 100, 100, 0.3)'
      } else if (intensity > 0.6) {
        gameBoard.style.boxShadow = 'var(--shadow-xl), 0 0 30px rgba(255, 150, 100, 0.2)'
      } else if (intensity > 0.35) {
        gameBoard.style.boxShadow = 'var(--shadow-lg), 0 0 20px rgba(200, 150, 255, 0.15)'
      } else {
        gameBoard.style.boxShadow = 'var(--shadow-neumorphism-light)'
      }
    }

    // 🌟 添加页面标题的动态效果（如果存在）
    const pageTitle = document.title
    if (intensity > 0.85) {
      document.title = '🔥 2048 - 极限模式！'
    } else if (intensity > 0.6) {
      document.title = '⚡ 2048 - 紧张刺激'
    } else if (intensity > 0.35) {
      document.title = '🎯 2048 - 升温中'
    } else {
      document.title = '🎮 2048 游戏'
    }
  }
}

// 动画效果函数
const triggerScoreAnimation = () => {
  if (scoreValue.value) {
    // 根据分数强度选择不同的动画效果
    const intensity = scoreIntensity.value

    if (intensity > 0.8) {
      scoreValue.value.classList.add('score-increase-intense')
    } else if (intensity > 0.5) {
      scoreValue.value.classList.add('score-increase-heated')
    } else {
      scoreValue.value.classList.add('score-increase')
    }

    setTimeout(() => {
      scoreValue.value?.classList.remove('score-increase', 'score-increase-heated', 'score-increase-intense')
    }, 500)
  }
}

const triggerShakeAnimation = () => {
  if (gameBoard.value) {
    gameBoard.value.classList.add('shake')
    setTimeout(() => {
      gameBoard.value?.classList.remove('shake')
    }, 500)
  }
}

// {{ AURA-X: Modify - 增强合并动画函数，支持多级动画效果. Approval: 寸止(ID:1678886433). }}
const triggerMergeAnimation = (row: number, col: number, value: number) => {
  const tileElement = document.querySelector(`.tile-position-${row}-${col}`)

  if (tileElement) {
    // 移除可能存在的旧动画类
    tileElement.classList.remove('tile-merge', 'tile-merge-high', 'tile-merge-super')

    // 根据合并后的数值选择动画类型
    let animationType = 'tile-merge'

    if (value >= 1024) {
      // 超高数值使用超级动画
      animationType = 'tile-merge-super'
    } else if (value >= 128) {
      // 高数值使用增强动画
      animationType = 'tile-merge-high'
    } else {
      // 普通数值使用基础动画
      animationType = 'tile-merge'
    }

    tileElement.classList.add(animationType)

    // 🎆 添加粒子效果（仅限高数值）
    if (value >= 256) {
      createParticleEffect(tileElement as HTMLElement, value)
    }

    // 动画完成后清理类名
    const duration = value >= 1024 ? 800 : value >= 128 ? 500 : 300
    setTimeout(() => {
      tileElement.classList.remove(animationType)
    }, duration)
  }
}

// {{ AURA-X: Modify - 优化粒子效果，支持性能设置. Approval: 寸止(ID:1678886438). }}
// 🎆 创建粒子效果
const createParticleEffect = (element: HTMLElement, value: number) => {
  // 检查是否启用粒子效果
  if (!animationSettings.value.particles) {
    return
  }

  const rect = element.getBoundingClientRect()

  // 根据性能设置调整粒子数量
  let baseParticleCount = Math.min(value / 64, 12)
  switch (animationSettings.value.quality) {
    case 'low':
      baseParticleCount = Math.min(baseParticleCount / 3, 4)
      break
    case 'medium':
      baseParticleCount = Math.min(baseParticleCount / 2, 8)
      break
    case 'high':
      // 保持原始数量
      break
  }

  const particleCount = Math.max(1, Math.floor(baseParticleCount))

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div')
    particle.className = `merge-particle value-${value}`

    // 根据数值设置粒子颜色类
    if (value >= 2048) {
      particle.classList.add('value-2048')
    } else if (value >= 1024) {
      particle.classList.add('value-1024')
    } else if (value >= 512) {
      particle.classList.add('value-512')
    } else if (value >= 256) {
      particle.classList.add('value-256')
    }

    // 设置粒子样式
    const size = animationSettings.value.quality === 'low' ? 3 : 4
    particle.style.cssText = `
      position: fixed;
      width: ${size}px;
      height: ${size}px;
      border-radius: 50%;
      pointer-events: none;
      z-index: 1000;
      left: ${rect.left + rect.width / 2}px;
      top: ${rect.top + rect.height / 2}px;
    `

    document.body.appendChild(particle)

    // 随机方向和距离
    const angle = (Math.PI * 2 * i) / particleCount
    const distance = animationSettings.value.quality === 'low' ? 20 : 30 + Math.random() * 40
    const endX = rect.left + rect.width / 2 + Math.cos(angle) * distance
    const endY = rect.top + rect.height / 2 + Math.sin(angle) * distance

    // 根据性能设置调整动画时长
    const duration = animationSettings.value.quality === 'low' ? 400 : 600

    // 粒子动画
    particle.animate([
      {
        transform: 'translate(0, 0) scale(1)',
        opacity: 1
      },
      {
        transform: `translate(${endX - (rect.left + rect.width / 2)}px, ${endY - (rect.top + rect.height / 2)}px) scale(0)`,
        opacity: 0
      }
    ], {
      duration,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    }).onfinish = () => {
      if (document.body.contains(particle)) {
        document.body.removeChild(particle)
      }
    }
  }
}

// 生命周期钩子
// {{ AURA-X: Modify - 组件挂载时增加性能检测和优化. Approval: 寸止(ID:1678886437). }}
onMounted(() => {
  initGame()
  window.addEventListener('keydown', handleKeydown)

  // 🧪 开发模式下测试对比度
  if (isDevelopment.value) {
    testContrastRatios()
  }

  // 🎯 添加可访问性增强
  addAccessibilityEnhancements()

  // 🎛️ 检测设备性能并优化动画
  detectDevicePerformance()

  // 💾 加载用户动画偏好设置
  loadAnimationPreferences()

  // 🎯 加载触控设置
  loadTouchSettings()

  // 🎹 初始化键盘导航
  initKeyboardNavigation()

  // 开发测试：添加全局测试函数
  if (typeof window !== 'undefined') {
    (window as any).testDynamicColors = () => {
      console.log('🎨 测试动态颜色系统...')
      const testScores = [0, 500, 1500, 3000, 8000, 12000, 20000, 50000]
      let index = 0

      const testInterval = setInterval(() => {
        if (index >= testScores.length) {
          clearInterval(testInterval)
          console.log('✅ 动态颜色测试完成')
          return
        }

        score.value = testScores[index]
        updateScoreIntensity()
        triggerScoreAnimation()
        console.log(`分数: ${score.value}, 强度: ${scoreIntensity.value.toFixed(2)}`)
        index++
      }, 2000)
    }
  }
})

// 🎯 可访问性增强功能
const addAccessibilityEnhancements = () => {
  // 为游戏板添加ARIA标签
  if (gameBoard.value) {
    gameBoard.value.setAttribute('role', 'grid')
    gameBoard.value.setAttribute('aria-label', '2048游戏板，使用方向键或滑动来移动方块')
  }

  // 为分数容器添加ARIA标签
  const scoreContainer = document.querySelector('.score-container')
  if (scoreContainer) {
    scoreContainer.setAttribute('role', 'status')
    scoreContainer.setAttribute('aria-live', 'polite')
  }

  // 添加高对比度模式检测
  const prefersHighContrast = window.matchMedia('(prefers-contrast: high)')
  if (prefersHighContrast.matches) {
    document.documentElement.classList.add('high-contrast-mode')
  }

  // 监听高对比度偏好变化
  prefersHighContrast.addEventListener('change', (e) => {
    if (e.matches) {
      document.documentElement.classList.add('high-contrast-mode')
    } else {
      document.documentElement.classList.remove('high-contrast-mode')
    }
  })

  // 添加减少动画偏好检测
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)')
  if (prefersReducedMotion.matches) {
    document.documentElement.classList.add('reduced-motion')
  }

  prefersReducedMotion.addEventListener('change', (e) => {
    if (e.matches) {
      document.documentElement.classList.add('reduced-motion')
    } else {
      document.documentElement.classList.remove('reduced-motion')
    }
  })
}

// 💾 保存动画偏好设置
const saveAnimationPreferences = () => {
  try {
    localStorage.setItem('game2048_animation_settings', JSON.stringify(animationSettings.value))
  } catch (error) {
    console.warn('无法保存动画设置:', error)
  }
}

// 💾 加载动画偏好设置
const loadAnimationPreferences = () => {
  try {
    const saved = localStorage.getItem('game2048_animation_settings')
    if (saved) {
      const settings = JSON.parse(saved)
      animationSettings.value = { ...animationSettings.value, ...settings }
      applyAnimationSettings()
    }
  } catch (error) {
    console.warn('无法加载动画设置:', error)
  }
}

// 🎛️ 切换动画设置
const toggleAnimations = () => {
  animationSettings.value.enabled = !animationSettings.value.enabled
  applyAnimationSettings()
  saveAnimationPreferences()
}

const setAnimationQuality = (quality: 'low' | 'medium' | 'high') => {
  animationSettings.value.quality = quality
  applyAnimationSettings()
  saveAnimationPreferences()
}

const toggleParticles = () => {
  animationSettings.value.particles = !animationSettings.value.particles
  applyAnimationSettings()
  saveAnimationPreferences()
}

const toggleBackgroundEffects = () => {
  animationSettings.value.backgroundEffects = !animationSettings.value.backgroundEffects
  applyAnimationSettings()
  saveAnimationPreferences()
}

// 🎯 性能监控
const performanceMonitor = ref({
  fps: 60,
  frameTime: 16.67,
  isLagging: false
})

let lastFrameTime = performance.now()
let frameCount = 0
let fpsUpdateTime = performance.now()

const monitorPerformance = () => {
  const now = performance.now()
  const deltaTime = now - lastFrameTime
  lastFrameTime = now
  frameCount++

  // 每秒更新一次FPS
  if (now - fpsUpdateTime >= 1000) {
    performanceMonitor.value.fps = Math.round((frameCount * 1000) / (now - fpsUpdateTime))
    performanceMonitor.value.frameTime = deltaTime
    performanceMonitor.value.isLagging = performanceMonitor.value.fps < 30

    // 如果性能不佳，自动降低动画质量
    if (performanceMonitor.value.isLagging && animationSettings.value.quality === 'high') {
      console.warn('检测到性能问题，自动降低动画质量')
      setAnimationQuality('medium')
    }

    frameCount = 0
    fpsUpdateTime = now
  }

  requestAnimationFrame(monitorPerformance)
}

// 启动性能监控
if (typeof window !== 'undefined') {
  requestAnimationFrame(monitorPerformance)
}

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* {{ AURA-X: Add - 引入现代化CSS变量设计令牌系统. Approval: 寸止(ID:1678886400). }} */
/* {{ Source: context7-mcp on 'Modern UI Design Tokens' }} */
:root {
  /* 🎨 颜色系统 - 基于HSL的现代调色板 */
  --color-primary-hue: 240;
  --color-primary-sat: 60%;
  --color-primary-light: 50%;
  --color-primary: hsl(var(--color-primary-hue), var(--color-primary-sat), var(--color-primary-light));
  --color-primary-light: hsl(var(--color-primary-hue), var(--color-primary-sat), 70%);
  --color-primary-dark: hsl(var(--color-primary-hue), var(--color-primary-sat), 30%);

  /* 中性色调 */
  --color-white: hsl(0, 0%, 100%);
  --color-white-soft: hsl(0, 0%, 98%);
  --color-white-mute: hsl(0, 0%, 95%);
  --color-gray-50: hsl(220, 14%, 96%);
  --color-gray-100: hsl(220, 14%, 93%);
  --color-gray-200: hsl(220, 13%, 91%);
  --color-gray-300: hsl(216, 12%, 84%);
  --color-gray-400: hsl(218, 11%, 65%);
  --color-gray-500: hsl(220, 9%, 46%);
  --color-gray-600: hsl(215, 14%, 34%);
  --color-gray-700: hsl(217, 19%, 27%);
  --color-gray-800: hsl(215, 28%, 17%);
  --color-gray-900: hsl(221, 39%, 11%);

  /* 语义化颜色 */
  --color-success: hsl(142, 76%, 36%);
  --color-success-light: hsl(142, 76%, 46%);
  --color-warning: hsl(38, 92%, 50%);
  --color-warning-light: hsl(38, 92%, 60%);
  --color-error: hsl(0, 84%, 60%);
  --color-error-light: hsl(0, 84%, 70%);
  --color-info: hsl(199, 89%, 48%);
  --color-info-light: hsl(199, 89%, 58%);

  /* 🌈 动态颜色强度映射 */
  --intensity-calm: hsl(240, 60%, 70%);
  --intensity-warm: hsl(280, 70%, 65%);
  --intensity-hot: hsl(20, 80%, 60%);
  --intensity-extreme: hsl(0, 90%, 55%);

  /* 📏 间距系统 - 基于8px网格 */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 0.75rem;   /* 12px */
  --space-lg: 1rem;      /* 16px */
  --space-xl: 1.5rem;    /* 24px */
  --space-2xl: 2rem;     /* 32px */
  --space-3xl: 3rem;     /* 48px */
  --space-4xl: 4rem;     /* 64px */

  /* 🔤 字体系统 */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* 🔄 圆角系统 */
  --radius-xs: 0.25rem;  /* 4px */
  --radius-sm: 0.5rem;   /* 8px */
  --radius-md: 0.75rem;  /* 12px */
  --radius-lg: 1rem;     /* 16px */
  --radius-xl: 1.25rem;  /* 20px */
  --radius-2xl: 1.5rem;  /* 24px */
  --radius-full: 9999px;

  /* 🌫️ 阴影系统 - 新拟物风格 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  /* {{ AURA-X: Modify - 增强新拟物风格阴影系统，添加更多层次和光效. Approval: 寸止(ID:1678886418). }} */
  /* 新拟物风格阴影 - 多层次系统 */
  --shadow-neumorphism-light:
    12px 12px 24px rgba(163, 177, 198, 0.4),
    -12px -12px 24px rgba(255, 255, 255, 0.6),
    inset 1px 1px 2px rgba(255, 255, 255, 0.3);

  --shadow-neumorphism-dark:
    16px 16px 32px rgba(163, 177, 198, 0.5),
    -16px -16px 32px rgba(255, 255, 255, 0.4),
    inset 2px 2px 4px rgba(255, 255, 255, 0.2);

  --shadow-neumorphism-inset:
    inset 8px 8px 16px rgba(163, 177, 198, 0.3),
    inset -8px -8px 16px rgba(255, 255, 255, 0.7),
    inset 1px 1px 2px rgba(163, 177, 198, 0.2);

  /* 新拟物风格按钮阴影 */
  --shadow-neumorphism-button:
    6px 6px 12px rgba(163, 177, 198, 0.4),
    -6px -6px 12px rgba(255, 255, 255, 0.6),
    inset 1px 1px 2px rgba(255, 255, 255, 0.4);

  --shadow-neumorphism-button-pressed:
    inset 4px 4px 8px rgba(163, 177, 198, 0.4),
    inset -4px -4px 8px rgba(255, 255, 255, 0.6);

  /* 新拟物风格浮动效果 */
  --shadow-neumorphism-float:
    20px 20px 40px rgba(163, 177, 198, 0.3),
    -20px -20px 40px rgba(255, 255, 255, 0.5),
    inset 2px 2px 4px rgba(255, 255, 255, 0.3);

  /* 光效系统 */
  --glow-soft: 0 0 20px rgba(255, 255, 255, 0.3);
  --glow-medium: 0 0 30px rgba(255, 255, 255, 0.4);
  --glow-strong: 0 0 40px rgba(255, 255, 255, 0.5);
  --glow-colored-blue: 0 0 25px rgba(102, 126, 234, 0.4);
  --glow-colored-purple: 0 0 25px rgba(139, 92, 246, 0.4);
  --glow-colored-pink: 0 0 25px rgba(236, 72, 153, 0.4);
  --glow-colored-orange: 0 0 25px rgba(251, 146, 60, 0.4);

  /* ⚡ 动画系统 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-slower: 0.8s;
  --easing-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* 🎯 Z-index 层级系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* 📱 断点系统 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

.game-container {
  max-width: 500px;
  margin: 0 auto;
  padding: var(--space-md);
  font-family: var(--font-family-primary);
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--space-md);
  transition: all var(--duration-slower) var(--easing-ease);
  overflow-x: hidden;
  position: relative;
}

/* {{ AURA-X: Modify - 增强游戏容器动态背景状态，添加更多强度级别. Approval: 寸止(ID:1678886411). }} */
/* 🎮 游戏容器动态背景状态 - 多级强度系统 */
.game-container.game-calm {
  background: linear-gradient(135deg,
    hsl(240, 60%, 95%) 0%,
    hsl(250, 55%, 97%) 100%);
  box-shadow: var(--shadow-sm);
}

.game-container.game-warm {
  background: linear-gradient(135deg,
    hsl(220, 65%, 90%) 0%,
    hsl(260, 60%, 92%) 100%);
  box-shadow: var(--shadow-md);
  animation: container-breathe 4s ease-in-out infinite;
}

.game-container.game-heated {
  background: linear-gradient(135deg, var(--intensity-warm) 0%, var(--intensity-hot) 100%);
  box-shadow: var(--shadow-lg);
  animation: container-pulse-light 3s ease-in-out infinite;
}

.game-container.game-intense {
  background: linear-gradient(135deg, var(--intensity-hot) 0%, var(--intensity-extreme) 100%);
  animation: container-pulse-medium 2s ease-in-out infinite;
  box-shadow: var(--shadow-xl);
}

.game-container.game-extreme {
  background: linear-gradient(135deg,
    hsl(0, 95%, 55%) 0%,
    hsl(15, 90%, 60%) 25%,
    hsl(30, 85%, 65%) 50%,
    hsl(45, 80%, 70%) 75%,
    hsl(60, 75%, 75%) 100%);
  animation: container-pulse-intense 1.5s ease-in-out infinite, container-glow 3s ease-in-out infinite;
  box-shadow: var(--shadow-2xl);
}

/* 🌊 容器动画效果 */
@keyframes container-breathe {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.002);
    filter: brightness(1.05);
  }
}

@keyframes container-pulse-light {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-lg);
  }
  50% {
    transform: scale(1.003);
    box-shadow: var(--shadow-xl), 0 0 20px rgba(200, 150, 255, 0.2);
  }
}

@keyframes container-pulse-medium {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-xl);
  }
  50% {
    transform: scale(1.005);
    box-shadow: var(--shadow-2xl), 0 0 25px rgba(255, 150, 100, 0.3);
  }
}

@keyframes container-pulse-intense {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-2xl);
  }
  50% {
    transform: scale(1.008);
    box-shadow: var(--shadow-2xl), 0 0 35px rgba(255, 100, 100, 0.4);
  }
}

@keyframes container-glow {
  0%, 100% {
    filter: brightness(1) saturate(1);
  }
  50% {
    filter: brightness(1.1) saturate(1.2);
  }
}

/* 主布局容器 */
.game-container {
  display: flex;
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  align-items: flex-start;
}

.main-game-area {
  flex: 0 0 auto;
}

/* 右侧栏样式 */
.sidebar-right {
  flex: 0 0 280px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.control-hints,
.announcement-board {
  background:
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 255, 0.9) 30%,
      rgba(240, 245, 255, 0.85) 70%,
      rgba(235, 240, 255, 0.8) 100%);
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 20px 60px rgba(102, 126, 234, 0.15),
    0 8px 24px rgba(102, 126, 234, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: #4a5568;
}

.title-icon {
  font-size: 20px;
}

/* 键盘控制提示样式 */
.hint-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.key-combo {
  display: flex;
  gap: 4px;
}

.key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  padding: 0 8px;
  background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.hint-text {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* 公告栏样式 */
.announcement-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.announcement-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.announcement-icon {
  flex: 0 0 auto;
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.announcement-content {
  flex: 1;
}

.announcement-title {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 4px;
}

.announcement-text {
  font-size: 12px;
  color: #718096;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .game-container {
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }

  .sidebar-right {
    flex: none;
    width: 100%;
    max-width: 500px;
    flex-direction: row;
    gap: 16px;
  }

  .control-hints,
  .announcement-board {
    flex: 1;
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .sidebar-right {
    flex-direction: column;
    gap: 16px;
  }

  .control-hints,
  .announcement-board {
    padding: 16px;
  }

  .sidebar-title {
    font-size: 16px;
  }

  .announcement-item {
    padding: 10px;
  }
}

/* {{ AURA-X: Modify - 使用设计令牌优化分数容器样式. Approval: 寸止(ID:1678886402). }} */
.score-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2xl);
  gap: var(--space-lg);
}

/* {{ AURA-X: Modify - 增强分数框新拟物风格，添加多层质感和光效. Approval: 寸止(ID:1678886421). }} */
.score-box {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 248, 248, 0.95) 100%);
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--radius-lg);
  color: var(--color-primary);
  text-align: center;
  min-width: 100px;
  box-shadow: var(--shadow-neumorphism-button);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all var(--duration-normal) var(--easing-ease);
  position: relative;
  overflow: hidden;
}

/* 顶部高光条 */
.score-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

/* 动态光效扫描 */
.score-box::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-slow) var(--easing-ease);
  pointer-events: none;
}

.score-box:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-blue);
}

.score-box:hover::after {
  left: 100%;
}

.score-box:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-neumorphism-button-pressed);
}

.score-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  margin-bottom: var(--space-sm);
  letter-spacing: 0.1em;
  opacity: 0.8;
}

.score-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  letter-spacing: -0.02em;
}

/* {{ AURA-X: Modify - 增强游戏板新拟物风格，添加更丰富的光效和质感. Approval: 寸止(ID:1678886419). }} */
.game-board {
  position: relative;
  background:
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 255, 0.9) 30%,
      rgba(240, 245, 255, 0.85) 70%,
      rgba(235, 240, 255, 0.8) 100%);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow:
    0 20px 60px rgba(102, 126, 234, 0.15),
    0 8px 24px rgba(102, 126, 234, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
  max-width: 100%;
  width: 496px;
  height: 496px;
  margin-left: auto;
  margin-right: auto;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.game-board::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent,
    rgba(102, 126, 234, 0.3) 20%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(102, 126, 234, 0.3) 80%,
    transparent);
  border-radius: 20px 20px 0 0;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.game-board::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: calc(var(--radius-xl) - 2px);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.game-board:hover {
  box-shadow: var(--shadow-neumorphism-float);
  transform: translateY(-2px);
}

.game-board:active {
  box-shadow: var(--shadow-neumorphism-inset);
  transform: translateY(0);
}

.grid-container {
  position: relative;
  z-index: 1;
  width: 448px;
  height: 448px;
  display: block !important;
}

.grid-row {
  display: flex !important;
  margin-bottom: 16px;
  gap: 16px;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
}

.grid-row:last-child {
  margin-bottom: 0;
}

.grid-cell {
  position: relative;
  width: 100px;
  height: 100px;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(240, 242, 255, 0.6) 50%,
    rgba(220, 225, 255, 0.4) 100%);
  border-radius: 12px;
  border: none;
  box-shadow:
    inset 0 2px 8px rgba(102, 126, 234, 0.1),
    inset 0 -2px 4px rgba(255, 255, 255, 0.8),
    0 2px 8px rgba(102, 126, 234, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

/* {{ AURA-X: Modify - 使用设计令牌优化方块基础样式. Approval: 寸止(ID:1678886404). }} */
.tile-container {
  position: absolute;
  top: 24px;
  left: 24px;
  z-index: 2;
  width: 448px;
  height: 448px;
}

/* {{ AURA-X: Modify - 增强方块新拟物风格，添加多层光效和质感. Approval: 寸止(ID:1678886420). }} */
.tile {
  position: absolute;
  width: 98px;
  height: 98px;
  border-radius: 12px;
  font-weight: 800;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  letter-spacing: -0.02em;
  border: none;
  overflow: hidden;
  z-index: 2;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  transform: scale(1);
}

/* 确保方块文字在最上层并可见 */
.tile {
  color: inherit !important;
  font-weight: var(--font-weight-extrabold) !important;
  text-align: center !important;
}

.tile > * {
  position: relative;
  z-index: 10;
}

/* 通用方块文字样式 - 确保所有方块文字可见 */
.tile[class*="tile-"] {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: inherit !important;
}

/* 顶部高光效果 */
.tile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  pointer-events: none;
  z-index: -1;
}

/* 动态光效 */
.tile::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.15) 50%, transparent 70%);
  transform: rotate(45deg);
  transition: all var(--duration-slow) var(--easing-ease);
  opacity: 0;
  pointer-events: none;
  z-index: -1;
}

.tile:hover {
  transform: scale(1.08) translateY(-6px);
  z-index: 10;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.25),
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.tile:hover::after {
  opacity: 0.8;
  animation: shine 1.5s ease-in-out;
}

.tile:active {
  transform: scale(0.95) translateY(-2px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 方块位置定义 - 居中对齐到网格单元格 */
.tile-position-0-0 { top: 1px; left: 1px; }
.tile-position-0-1 { top: 1px; left: 117px; }
.tile-position-0-2 { top: 1px; left: 233px; }
.tile-position-0-3 { top: 1px; left: 349px; }
.tile-position-1-0 { top: 117px; left: 1px; }
.tile-position-1-1 { top: 117px; left: 117px; }
.tile-position-1-2 { top: 117px; left: 233px; }
.tile-position-1-3 { top: 117px; left: 349px; }
.tile-position-2-0 { top: 233px; left: 1px; }
.tile-position-2-1 { top: 233px; left: 117px; }
.tile-position-2-2 { top: 233px; left: 233px; }
.tile-position-2-3 { top: 233px; left: 349px; }
.tile-position-3-0 { top: 349px; left: 1px; }
.tile-position-3-1 { top: 349px; left: 117px; }
.tile-position-3-2 { top: 349px; left: 233px; }
.tile-position-3-3 { top: 349px; left: 349px; }

/* {{ AURA-X: Modify - WCAG AA级对比度优化方块颜色，确保可访问性. Approval: 寸止(ID:1678886414). }} */
/* {{ Source: context7-mcp on 'WCAG Accessibility Contrast Standards' }} */
/* 🎯 所有方块颜色均经过对比度验证，确保至少4.5:1的对比度比率 */

/* {{ AURA-X: Modify - 为低数值方块添加新拟物风格和特殊光效. Approval: 寸止(ID:1678886423). }} */
.tile-2 {
  background: linear-gradient(145deg, hsl(240, 100%, 98%) 0%, hsl(240, 80%, 95%) 100%);
  color: hsl(240, 80%, 20%) !important; /* 对比度: 8.2:1 ✅ */
  box-shadow: var(--shadow-neumorphism-button), var(--glow-soft);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tile-2:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-blue);
}

.tile-4 {
  background: linear-gradient(145deg, hsl(240, 100%, 92%) 0%, hsl(240, 70%, 85%) 100%);
  color: hsl(240, 90%, 15%) !important; /* 对比度: 9.1:1 ✅ */
  box-shadow: var(--shadow-neumorphism-button), var(--glow-soft);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tile-4:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-blue);
}

.tile-8 {
  background: linear-gradient(145deg, hsl(45, 95%, 88%) 0%, hsl(45, 85%, 75%) 100%);
  color: hsl(30, 100%, 15%) !important; /* 对比度: 7.8:1 ✅ */
  box-shadow: var(--shadow-neumorphism-button), var(--glow-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.tile-8:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-orange);
}

.tile-16 {
  background: linear-gradient(145deg, hsl(25, 95%, 85%) 0%, hsl(25, 85%, 70%) 100%);
  color: hsl(25, 100%, 10%) !important; /* 对比度: 10.2:1 ✅ */
  box-shadow: var(--shadow-neumorphism-button), var(--glow-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.tile-16:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-orange);
}

.tile-32 {
  background: linear-gradient(145deg, hsl(0, 85%, 85%) 0%, hsl(0, 75%, 75%) 100%);
  color: hsl(0, 100%, 10%) !important; /* 对比度: 9.8:1 ✅ */
  box-shadow: var(--shadow-neumorphism-button), var(--glow-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.tile-32:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-pink);
}

.tile-64 {
  background: linear-gradient(145deg, hsl(270, 85%, 92%) 0%, hsl(270, 75%, 85%) 100%);
  color: hsl(270, 100%, 10%); /* 对比度: 11.5:1 ✅ */
  box-shadow: var(--shadow-neumorphism-button), var(--glow-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tile-64:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-purple);
}

/* {{ AURA-X: Modify - 高数值方块增强新拟物风格和特殊光效. Approval: 寸止(ID:1678886424). }} */
.tile-128 {
  background: linear-gradient(145deg, hsl(142, 85%, 90%) 0%, hsl(142, 75%, 80%) 100%);
  color: hsl(142, 100%, 10%); /* 对比度: 12.1:1 ✅ */
  font-size: var(--font-size-3xl);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 15px rgba(22, 101, 52, 0.4);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: tile-breathe 4s ease-in-out infinite;
}

.tile-128:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 25px rgba(22, 101, 52, 0.6);
}

.tile-256 {
  background: linear-gradient(145deg, hsl(210, 85%, 90%) 0%, hsl(210, 75%, 80%) 100%);
  color: hsl(210, 100%, 10%); /* 对比度: 13.2:1 ✅ */
  font-size: var(--font-size-3xl);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 15px rgba(30, 64, 175, 0.4);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: tile-breathe 4s ease-in-out infinite;
}

.tile-256:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 25px rgba(30, 64, 175, 0.6);
}

.tile-512 {
  background: linear-gradient(145deg, hsl(50, 95%, 85%) 0%, hsl(50, 85%, 70%) 100%);
  color: hsl(40, 100%, 10%); /* 对比度: 11.8:1 ✅ */
  font-size: var(--font-size-3xl);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 20px rgba(161, 98, 7, 0.5);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.25);
  animation: tile-pulse 3s ease-in-out infinite;
}

.tile-512:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 30px rgba(161, 98, 7, 0.7);
}

.tile-1024 {
  background: linear-gradient(145deg, hsl(30, 95%, 85%) 0%, hsl(30, 85%, 70%) 100%);
  color: hsl(30, 100%, 8%); /* 对比度: 14.5:1 ✅ */
  font-size: var(--font-size-2xl);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 25px rgba(194, 65, 12, 0.6);
  animation: tile-glow 3s ease-in-out infinite, tile-pulse 2s ease-in-out infinite;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tile-1024:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 35px rgba(194, 65, 12, 0.8);
}

.tile-2048 {
  background: linear-gradient(145deg, hsl(320, 85%, 92%) 0%, hsl(320, 75%, 85%) 100%);
  color: hsl(320, 100%, 8%); /* 对比度: 15.8:1 ✅ */
  font-size: var(--font-size-2xl);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 30px rgba(190, 24, 93, 0.7);
  animation: pulse-glow 2s infinite, tile-glow 3s ease-in-out infinite, tile-rainbow 5s linear infinite;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tile-2048:hover {
  box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 40px rgba(190, 24, 93, 0.9);
}

/* {{ AURA-X: Modify - 使用设计令牌优化动画效果，增加新的发光动画. Approval: 寸止(ID:1678886407). }} */
/* 2048方块的特殊发光效果 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: var(--shadow-2xl), 0 0 20px rgba(190, 24, 93, 0.3), 0 0 0 3px rgba(190, 24, 93, 0.4);
  }
  50% {
    box-shadow: var(--shadow-2xl), 0 0 30px rgba(190, 24, 93, 0.5), 0 0 0 3px rgba(190, 24, 93, 0.6);
  }
}

/* {{ AURA-X: Add - 新增多种新拟物风格动画效果. Approval: 寸止(ID:1678886425). }} */
/* 新增：高数值方块的发光效果 */
@keyframes tile-glow {
  0%, 100% {
    filter: brightness(1) saturate(1);
  }
  50% {
    filter: brightness(1.1) saturate(1.2);
  }
}

/* 呼吸动画 - 轻微的缩放效果 */
@keyframes tile-breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* 脉冲动画 - 更明显的缩放效果 */
@keyframes tile-pulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.05);
    filter: brightness(1.1);
  }
}

/* 彩虹光效 - 2048方块专用 */
@keyframes tile-rainbow {
  0% { filter: hue-rotate(0deg); }
  25% { filter: hue-rotate(90deg); }
  50% { filter: hue-rotate(180deg); }
  75% { filter: hue-rotate(270deg); }
  100% { filter: hue-rotate(360deg); }
}

/* 新拟物风格浮动效果 */
@keyframes neumorphism-float {
  0%, 100% {
    box-shadow: var(--shadow-neumorphism-light);
    transform: translateY(0);
  }
  50% {
    box-shadow: var(--shadow-neumorphism-float);
    transform: translateY(-4px);
  }
}

/* {{ AURA-X: Modify - 增强方块出现效果，支持多种动画类型. Approval: 寸止(ID:1678886429). }} */
/* 🎭 方块出现动画类 */
.tile-enter-active {
  animation: tile-appear var(--duration-slow) var(--easing-bounce);
}

.tile-appear-default {
  animation: tile-appear var(--duration-slow) var(--easing-bounce);
}

.tile-appear-spin {
  animation: tile-appear-spin var(--duration-slow) var(--easing-ease);
}

.tile-appear-drop {
  animation: tile-appear-drop var(--duration-slower) var(--easing-bounce);
}

.tile-appear-burst {
  animation: tile-appear-burst var(--duration-normal) var(--easing-ease);
}

/* 特殊数值的专属出现动画 */
.tile-2.tile-new {
  animation: tile-appear var(--duration-slow) var(--easing-bounce);
}

.tile-4.tile-new {
  animation: tile-appear-spin var(--duration-slow) var(--easing-ease);
}

/* 高数值方块的特殊出现效果 */
.tile-128.tile-new,
.tile-256.tile-new,
.tile-512.tile-new {
  animation: tile-appear-burst var(--duration-normal) var(--easing-ease);
}

.tile-1024.tile-new,
.tile-2048.tile-new {
  animation: tile-appear-burst var(--duration-normal) var(--easing-ease), tile-glow 2s ease-in-out;
}

.tile:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: var(--shadow-2xl);
}

/* {{ AURA-X: Modify - 增强方块合并动画，添加多层次特效. Approval: 寸止(ID:1678886431). }} */
/* 🎆 方块合并动画 - 多层次特效系统 */

/* 基础合并动画 */
.tile-merge {
  animation: tile-merge var(--duration-normal) var(--easing-bounce);
  z-index: 15;
}

@keyframes tile-merge {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  30% {
    transform: scale(1.15);
    filter: brightness(1.3);
    box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 20px rgba(102, 126, 234, 0.6);
  }
  60% {
    transform: scale(0.95);
    filter: brightness(1.1);
    box-shadow: var(--shadow-neumorphism-button), var(--glow-medium);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
    box-shadow: var(--shadow-neumorphism-button);
  }
}

/* 高数值合并动画 - 更强烈的效果 */
.tile-merge-high {
  animation: tile-merge-high var(--duration-normal) var(--easing-bounce);
  z-index: 20;
}

@keyframes tile-merge-high {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1);
  }
  25% {
    transform: scale(1.2) rotate(2deg);
    filter: brightness(1.4) saturate(1.3);
    box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 30px rgba(255, 215, 0, 0.8);
  }
  50% {
    transform: scale(0.9) rotate(-1deg);
    filter: brightness(1.2) saturate(1.1);
    box-shadow: var(--shadow-neumorphism-button), var(--glow-medium);
  }
  75% {
    transform: scale(1.05) rotate(1deg);
    filter: brightness(1.1) saturate(1.05);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1);
    box-shadow: var(--shadow-neumorphism-button);
  }
}

/* 超级合并动画 - 1024/2048专用 */
.tile-merge-super {
  animation: tile-merge-super var(--duration-slow) var(--easing-bounce), tile-burst var(--duration-normal) var(--easing-ease);
  z-index: 25;
}

@keyframes tile-merge-super {
  0% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1) hue-rotate(0deg);
  }
  20% {
    transform: scale(1.3) rotate(5deg);
    filter: brightness(1.5) saturate(1.5) hue-rotate(30deg);
    box-shadow: var(--shadow-neumorphism-float), var(--glow-strong), 0 0 40px rgba(255, 100, 255, 0.9);
  }
  40% {
    transform: scale(0.8) rotate(-3deg);
    filter: brightness(1.3) saturate(1.3) hue-rotate(60deg);
  }
  60% {
    transform: scale(1.1) rotate(2deg);
    filter: brightness(1.2) saturate(1.2) hue-rotate(90deg);
  }
  80% {
    transform: scale(0.95) rotate(-1deg);
    filter: brightness(1.1) saturate(1.1) hue-rotate(120deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) saturate(1) hue-rotate(0deg);
    box-shadow: var(--shadow-neumorphism-button);
  }
}

/* 爆发效果动画 */
@keyframes tile-burst {
  0% {
    box-shadow: var(--shadow-neumorphism-button);
  }
  50% {
    box-shadow:
      var(--shadow-neumorphism-float),
      0 0 0 10px rgba(255, 255, 255, 0.3),
      0 0 0 20px rgba(255, 255, 255, 0.1),
      0 0 40px rgba(255, 215, 0, 0.6);
  }
  100% {
    box-shadow: var(--shadow-neumorphism-button);
  }
}

/* {{ AURA-X: Add - 粒子效果样式. Approval: 寸止(ID:1678886434). }} */
/* 🎆 粒子效果样式 */
.merge-particle {
  position: fixed;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  pointer-events: none;
  z-index: 1000;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.9) 0%, rgba(255, 165, 0, 0.7) 50%, transparent 100%);
  box-shadow: 0 0 6px rgba(255, 215, 0, 0.8);
}

/* 不同数值的粒子颜色 */
.merge-particle.value-256 {
  background: radial-gradient(circle, rgba(255, 100, 100, 0.9) 0%, rgba(255, 50, 50, 0.7) 50%, transparent 100%);
  box-shadow: 0 0 6px rgba(255, 100, 100, 0.8);
}

.merge-particle.value-512 {
  background: radial-gradient(circle, rgba(100, 255, 100, 0.9) 0%, rgba(50, 255, 50, 0.7) 50%, transparent 100%);
  box-shadow: 0 0 6px rgba(100, 255, 100, 0.8);
}

.merge-particle.value-1024 {
  background: radial-gradient(circle, rgba(100, 100, 255, 0.9) 0%, rgba(50, 50, 255, 0.7) 50%, transparent 100%);
  box-shadow: 0 0 8px rgba(100, 100, 255, 0.9);
}

.merge-particle.value-2048 {
  background: radial-gradient(circle, rgba(255, 100, 255, 0.9) 0%, rgba(255, 50, 255, 0.7) 50%, transparent 100%);
  box-shadow: 0 0 10px rgba(255, 100, 255, 1);
  animation: particle-rainbow 0.6s linear;
}

@keyframes particle-rainbow {
  0% { filter: hue-rotate(0deg); }
  25% { filter: hue-rotate(90deg); }
  50% { filter: hue-rotate(180deg); }
  75% { filter: hue-rotate(270deg); }
  100% { filter: hue-rotate(360deg); }
}

/* {{ AURA-X: Modify - 增强方块出现动画，添加多种出现效果. Approval: 寸止(ID:1678886428). }} */
/* 🌟 方块出现动画 - 多种效果 */

/* 默认出现动画 - 弹性缩放 */
@keyframes tile-appear {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
    filter: blur(4px);
  }
  60% {
    opacity: 0.8;
    transform: scale(1.15) rotate(0deg);
    filter: blur(1px);
  }
  80% {
    opacity: 0.95;
    transform: scale(0.95) rotate(0deg);
    filter: blur(0px);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: blur(0px);
  }
}

/* 旋转出现动画 */
@keyframes tile-appear-spin {
  0% {
    opacity: 0;
    transform: scale(0) rotate(180deg);
    filter: brightness(0.5);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotate(90deg);
    filter: brightness(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
}

/* 从上方掉落动画 */
@keyframes tile-appear-drop {
  0% {
    opacity: 0;
    transform: translateY(-100px) scale(0.8);
    filter: blur(2px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(10px) scale(1.05);
    filter: blur(0px);
  }
  80% {
    opacity: 0.95;
    transform: translateY(-5px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 光效爆发动画 */
@keyframes tile-appear-burst {
  0% {
    opacity: 0;
    transform: scale(0);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  30% {
    opacity: 0.6;
    transform: scale(1.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  }
  60% {
    opacity: 0.9;
    transform: scale(0.9);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.4);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: var(--shadow-neumorphism-button);
  }
}

/* {{ AURA-X: Modify - 优化方块移动动画，实现黄油般顺滑的效果. Approval: 寸止(ID:1678886426). }} */
/* 🎯 方块移动时的增强效果 - 多层次动画系统 */
.tile {
  transition:
    top var(--duration-normal) var(--easing-ease),
    left var(--duration-normal) var(--easing-ease),
    transform var(--duration-fast) var(--easing-ease),
    opacity var(--duration-fast) var(--easing-ease),
    box-shadow var(--duration-fast) var(--easing-ease);
  will-change: transform, top, left, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 移动中的方块状态 */
.tile.moving {
  z-index: 5;
  box-shadow: var(--shadow-neumorphism-float), var(--glow-medium);
  transform: translateZ(0) scale(1.02);
}

/* 方向性移动效果 */
.tile.moving-left {
  animation: slide-left var(--duration-normal) var(--easing-ease);
}

.tile.moving-right {
  animation: slide-right var(--duration-normal) var(--easing-ease);
}

.tile.moving-up {
  animation: slide-up var(--duration-normal) var(--easing-ease);
}

.tile.moving-down {
  animation: slide-down var(--duration-normal) var(--easing-ease);
}

/* 滑动动画关键帧 */
@keyframes slide-left {
  0% { transform: translateX(0) scale(1); }
  50% { transform: translateX(-5px) scale(1.02); }
  100% { transform: translateX(0) scale(1); }
}

@keyframes slide-right {
  0% { transform: translateX(0) scale(1); }
  50% { transform: translateX(5px) scale(1.02); }
  100% { transform: translateX(0) scale(1); }
}

@keyframes slide-up {
  0% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.02); }
  100% { transform: translateY(0) scale(1); }
}

@keyframes slide-down {
  0% { transform: translateY(0) scale(1); }
  50% { transform: translateY(5px) scale(1.02); }
  100% { transform: translateY(0) scale(1); }
}

.tile:active {
  transform: scale(0.95);
  transition-duration: var(--duration-fast);
}

/* 游戏板震动效果（当无法移动时） */
.game-board.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 分数增加动画 - 多级强度 */
.score-value.score-increase {
  animation: score-pop 0.3s ease-out;
}

.score-value.score-increase-heated {
  animation: score-pop-heated 0.4s ease-out;
}

.score-value.score-increase-intense {
  animation: score-pop-intense 0.5s ease-out;
}

@keyframes score-pop {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); color: #48bb78; }
  100% { transform: scale(1); }
}

@keyframes score-pop-heated {
  0% { transform: scale(1); }
  30% { transform: scale(1.15); color: #ff6b6b; }
  60% { transform: scale(1.05); color: #ff8e53; }
  100% { transform: scale(1); }
}

@keyframes score-pop-intense {
  0% { transform: scale(1); }
  20% { transform: scale(1.2); color: #ff4757; }
  40% { transform: scale(1.1); color: #ff3838; }
  60% { transform: scale(1.15); color: #ff4757; }
  80% { transform: scale(1.05); color: #ff6b6b; }
  100% { transform: scale(1); }
}

/* {{ AURA-X: Modify - 增强动态分数脉冲效果，添加多级强度. Approval: 寸止(ID:1678886412). }} */
/* 💫 动态分数脉冲效果 - 多级强度系统 */
.score-box.score-pulse-light {
  animation: score-pulse-light 3s ease-in-out infinite;
}

.score-box.score-pulse-medium {
  animation: score-pulse-medium 2s ease-in-out infinite;
}

.score-box.score-pulse-intense {
  animation: score-pulse-intense 1.5s ease-in-out infinite;
}

@keyframes score-pulse-light {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-neumorphism-light);
  }
  50% {
    transform: scale(1.015);
    box-shadow: var(--shadow-lg), 0 0 15px rgba(200, 150, 255, 0.2);
  }
}

@keyframes score-pulse-medium {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-neumorphism-light);
  }
  50% {
    transform: scale(1.025);
    box-shadow: var(--shadow-xl), 0 0 20px rgba(255, 150, 100, 0.3);
  }
}

@keyframes score-pulse-intense {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--shadow-neumorphism-light);
  }
  25% {
    transform: scale(1.03);
    box-shadow: var(--shadow-xl), 0 0 25px rgba(255, 100, 50, 0.4);
  }
  50% {
    transform: scale(1.04);
    box-shadow: var(--shadow-2xl), 0 0 30px rgba(255, 50, 0, 0.5);
  }
  75% {
    transform: scale(1.03);
    box-shadow: var(--shadow-xl), 0 0 25px rgba(255, 100, 50, 0.4);
  }
}

/* 分数容器的平滑过渡 */
.score-box {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.score-value {
  transition: color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 游戏恢复提示 */
.restore-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: rgba(72, 187, 120, 0.95);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-icon {
  font-size: 20px;
}

.notification-text {
  font-weight: 600;
  font-size: 14px;
}

/* 提示动画 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

.game-controls {
  text-align: center;
}

/* {{ AURA-X: Modify - 增强按钮新拟物风格，添加多层质感和交互效果. Approval: 寸止(ID:1678886422). }} */
.restart-btn {
  background: linear-gradient(145deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-ease);
  box-shadow: var(--shadow-neumorphism-button);
  letter-spacing: 0.01em;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 顶部高光效果 */
.restart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  pointer-events: none;
}

/* 动态光效扫描 */
.restart-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-slow) var(--easing-ease);
  pointer-events: none;
}

.restart-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-neumorphism-float), var(--glow-colored-blue);
}

.restart-btn:hover::after {
  left: 100%;
}

.restart-btn:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-neumorphism-button-pressed);
}

.restart-btn:focus {
  outline: none;
  box-shadow: var(--shadow-neumorphism-button), 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.auto-save-hint {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 15px;
  text-align: center;
  font-weight: 400;
}

/* 测试控件样式 */
.test-controls {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.test-controls h4 {
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 10px 0;
  font-size: 14px;
  text-align: center;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.test-btn, .test-btn-auto {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.test-btn-auto {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
}

.test-btn:hover, .test-btn-auto:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.test-btn-auto:hover {
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
}

.test-btn:active, .test-btn-auto:active {
  transform: translateY(0);
}

.game-over-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
  animation: overlay-appear 0.3s ease-out;
}

@keyframes overlay-appear {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

.game-over-message {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: message-appear 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes message-appear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.game-over-message h2 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
  font-size: 32px;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.game-over-message p {
  color: #4a5568;
  margin-bottom: 30px;
  font-size: 18px;
  font-weight: 500;
}

.game-over-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.submit-score-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.submit-score-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.score-submission {
  width: 100%;
}

.player-name-input {
  width: 100%;
  padding: 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 16px;
  margin-bottom: 20px;
  text-align: center;
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.player-name-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.submission-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 15px;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(160, 174, 192, 0.2);
}

.cancel-btn {
  background: linear-gradient(135deg, #fc8181 0%, #f56565 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(252, 129, 129, 0.3);
}

.cancel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(252, 129, 129, 0.4);
}

.submission-error {
  color: #e53e3e;
  text-align: center;
  font-size: 14px;
  margin-top: 15px;
  padding: 12px;
  background: rgba(254, 226, 226, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(252, 129, 129, 0.3);
  font-weight: 500;
}

.submission-success {
  color: #38a169;
  text-align: center;
  font-size: 14px;
  margin-top: 15px;
  padding: 12px;
  background: rgba(240, 253, 244, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(72, 187, 120, 0.3);
  font-weight: 500;
}

/* {{ AURA-X: Modify - 增强响应式设计，支持多种屏幕尺寸和横屏模式. Approval: 寸止(ID:1678886442). }} */
/* 📱 响应式设计 - 多级断点系统 */

/* 超小屏幕 (320px-480px) */
@media (max-width: 480px) {
  .game-container {
    max-width: 100%;
    width: 100%;
    padding: var(--space-xs);
    min-height: 100vh;
    justify-content: flex-start;
    gap: var(--space-sm);
    margin: 0;
  }

  .grid-cell, .tile {
    width: 55px;
    height: 55px;
    font-size: var(--font-size-base);
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: var(--font-size-base);
  }

  .tile-1024, .tile-2048 {
    font-size: var(--font-size-sm);
  }

  .score-container {
    flex-direction: row;
    gap: var(--space-xs);
    margin-bottom: var(--space-sm);
    width: 100%;
    justify-content: space-between;
  }

  .score-box {
    min-width: 60px;
    padding: var(--space-xs);
    flex: 1;
  }

  .score-label {
    font-size: var(--font-size-xs);
  }

  .score-value {
    font-size: var(--font-size-base);
  }

  .grid-row {
    gap: var(--space-xs);
    margin-bottom: var(--space-xs);
  }

  .game-board {
    padding: var(--space-sm);
    margin-bottom: var(--space-sm);
  }

  /* 小屏幕方块位置 - 基于55px网格和8px间距 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 63px; }
  .tile-position-0-2 { top: 0px; left: 126px; }
  .tile-position-0-3 { top: 0px; left: 189px; }
  .tile-position-1-0 { top: 63px; left: 0px; }
  .tile-position-1-1 { top: 63px; left: 63px; }
  .tile-position-1-2 { top: 63px; left: 126px; }
  .tile-position-1-3 { top: 63px; left: 189px; }
  .tile-position-2-0 { top: 126px; left: 0px; }
  .tile-position-2-1 { top: 126px; left: 63px; }
  .tile-position-2-2 { top: 126px; left: 126px; }
  .tile-position-2-3 { top: 126px; left: 189px; }
  .tile-position-3-0 { top: 189px; left: 0px; }
  .tile-position-3-1 { top: 189px; left: 63px; }
  .tile-position-3-2 { top: 189px; left: 126px; }
  .tile-position-3-3 { top: 189px; left: 189px; }
}

/* 小屏幕 (481px-600px) */
@media (min-width: 481px) and (max-width: 600px) {
  .game-container {
    max-width: 350px;
    padding: var(--space-md);
  }

  .grid-cell, .tile {
    width: 70px;
    height: 70px;
    font-size: var(--font-size-xl);
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: var(--font-size-lg);
  }

  .tile-1024, .tile-2048 {
    font-size: var(--font-size-base);
  }

  .score-container {
    gap: var(--space-md);
  }

  .score-box {
    min-width: 80px;
    padding: var(--space-md) var(--space-lg);
  }
}

/* 中等屏幕 (601px-768px) */
@media (min-width: 601px) and (max-width: 768px) {
  .game-container {
    max-width: 420px;
    padding: var(--space-lg);
  }

  .grid-cell, .tile {
    width: 85px;
    height: 85px;
    font-size: var(--font-size-2xl);
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: var(--font-size-xl);
  }

  .tile-1024, .tile-2048 {
    font-size: var(--font-size-lg);
  }
}

/* 大屏幕 (769px-1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .game-container {
    max-width: 500px;
    padding: var(--space-xl);
  }

  .grid-cell, .tile {
    width: 100px;
    height: 100px;
    font-size: var(--font-size-3xl);
  }
}

/* 超大屏幕 (1025px+) */
@media (min-width: 1025px) {
  .game-container {
    max-width: 600px;
    padding: var(--space-2xl);
  }

  .grid-cell, .tile {
    width: 120px;
    height: 120px;
    font-size: var(--font-size-4xl);
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: var(--font-size-3xl);
  }

  .tile-1024, .tile-2048 {
    font-size: var(--font-size-2xl);
  }

  .score-container {
    gap: var(--space-xl);
  }

  .score-box {
    min-width: 120px;
    padding: var(--space-lg) var(--space-2xl);
  }

  .score-value {
    font-size: var(--font-size-4xl);
  }
}

/* 🔄 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .game-container {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: var(--space-2xl);
    max-width: none;
    width: 100%;
    padding: var(--space-md);
  }

  .game-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .score-container {
    order: -1;
    flex-direction: column;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
  }

  .score-box {
    min-width: 100px;
    text-align: center;
  }

  .game-board {
    margin-bottom: var(--space-lg);
  }

  .restart-btn {
    margin-top: var(--space-md);
  }
}

/* 🔄 移动设备横屏特殊优化 */
@media (orientation: landscape) and (max-height: 500px) and (hover: none) {
  .game-container {
    min-height: 100vh;
    padding: var(--space-sm);
  }

  .grid-cell, .tile {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-base);
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: var(--font-size-sm);
  }

  .tile-1024, .tile-2048 {
    font-size: var(--font-size-xs);
  }

  .score-box {
    padding: var(--space-xs) var(--space-sm);
    min-width: 80px;
  }

  .score-label {
    font-size: var(--font-size-xs);
  }

  .score-value {
    font-size: var(--font-size-base);
  }
}

/* 📺 宽屏显示器优化 */
@media (min-width: 1200px) and (min-aspect-ratio: 16/9) {
  .game-container {
    max-width: 600px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-lg);
  }

  .game-content {
    display: flex;
    flex-direction: column;
  }

  .score-container {
    flex-direction: column;
    gap: var(--space-lg);
    min-width: 200px;
  }

  .score-box {
    padding: var(--space-xl) var(--space-2xl);
    min-width: 150px;
  }

  .score-label {
    font-size: var(--font-size-base);
  }

  .score-value {
    font-size: var(--font-size-4xl);
  }

  .restart-btn {
    margin-top: var(--space-xl);
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--font-size-lg);
  }
}

/* 📱 现代移动设备支持 - 动态视口高度 */
@supports (height: 100dvh) {
  .game-container {
    min-height: 100dvh; /* 动态视口高度，考虑移动浏览器地址栏 */
  }
}

/* 🖥️ 全屏状态优化 - 确保游戏内容可见 */
@media (min-width: 768px) and (min-height: 600px) {
  .game-container {
    justify-content: center;
    align-items: center;
    padding: var(--space-md);
    gap: var(--space-md);
  }

  .game-board {
    margin-bottom: var(--space-md);
  }
}

/* 🔧 容器查询支持（未来特性） */
@supports (container-type: inline-size) {
  .game-board {
    container-type: inline-size;
  }

  @container (max-width: 300px) {
    .grid-cell, .tile {
      width: 50px;
      height: 50px;
      font-size: var(--font-size-sm);
    }
  }
}

/* 🎯 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .tile {
    /* 高分辨率屏幕下的文字渲染优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .game-board {
    /* 高分辨率屏幕下的边框优化 */
    border-width: 0.5px;
  }
}

/* 🌙 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-white-soft: hsl(220, 14%, 8%);
    --color-white-mute: hsl(220, 13%, 12%);
    --color-gray-50: hsl(220, 14%, 15%);
    --color-gray-100: hsl(220, 14%, 20%);
  }

  .game-board {
    background: var(--color-gray-50);
    border-color: var(--color-gray-100);
  }

  .grid-cell {
    background: var(--color-gray-100);
    border-color: var(--color-gray-200);
  }

  .score-box {
    background: rgba(0, 0, 0, 0.8);
    color: var(--color-white);
    border-color: var(--color-gray-200);
  }
}

/* 🎮 游戏手柄支持检测 */
@media (pointer: coarse) and (hover: none) {
  .game-container {
    /* 触摸设备专用优化 */
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }
}

/* 📐 极端宽高比设备支持 */
@media (min-aspect-ratio: 21/9) {
  /* 超宽屏显示器 */
  .game-container {
    max-width: 600px;
  }
}

@media (max-aspect-ratio: 9/16) {
  /* 极窄屏幕（如折叠屏外屏） */
  .game-container {
    max-width: 280px;
  }

  .grid-cell, .tile {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-sm);
  }
}

/* 小屏幕 (481px-600px) */
@media (min-width: 481px) and (max-width: 600px) {
  .game-container {
    max-width: 350px;
    padding: var(--space-md);
  }

  .grid-cell, .tile {
    width: 70px;
    height: 70px;
    font-size: var(--font-size-xl);
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: var(--font-size-lg);
  }

  .tile-1024, .tile-2048 {
    font-size: var(--font-size-base);
  }

  /* 中等屏幕方块位置 - 基于70px网格和16px间距 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 86px; }
  .tile-position-0-2 { top: 0px; left: 172px; }
  .tile-position-0-3 { top: 0px; left: 258px; }
  .tile-position-1-0 { top: 86px; left: 0px; }
  .tile-position-1-1 { top: 86px; left: 86px; }
  .tile-position-1-2 { top: 86px; left: 172px; }
  .tile-position-1-3 { top: 86px; left: 258px; }
  .tile-position-2-0 { top: 172px; left: 0px; }
  .tile-position-2-1 { top: 172px; left: 86px; }
  .tile-position-2-2 { top: 172px; left: 172px; }
  .tile-position-2-3 { top: 172px; left: 258px; }
  .tile-position-3-0 { top: 258px; left: 0px; }
  .tile-position-3-1 { top: 258px; left: 86px; }
  .tile-position-3-2 { top: 258px; left: 172px; }
  .tile-position-3-3 { top: 258px; left: 258px; }

  .grid-cell {
    margin-right: 10px;
  }

  .score-box {
    padding: 8px 15px;
    min-width: 60px;
  }

  .score-label {
    font-size: 11px;
  }

  .score-value {
    font-size: 20px;
  }

  /* 游戏结束弹窗响应式 */
  .game-over-overlay {
    padding: 15px;
  }

  .game-over-message {
    padding: 20px;
    max-width: 95vw;
    max-height: 85vh;
  }

  .game-over-message h2 {
    font-size: 22px;
    margin-bottom: 10px;
  }

  .game-over-message p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .game-over-actions {
    flex-direction: column;
    gap: 15px;
  }

  .submit-score-btn, .restart-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .submission-actions {
    flex-direction: column;
    gap: 12px;
  }

  .submit-btn, .cancel-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
  }

  .player-name-input {
    padding: 14px;
    font-size: 16px;
    min-height: 48px;
  }
}

@media (max-width: 400px) {
  .game-container {
    max-width: 100%;
    width: 100%;
    padding: 6px;
    margin: 0;
    gap: 6px;
  }

  .grid-cell, .tile {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }

  .tile-128, .tile-256, .tile-512 {
    font-size: 18px;
  }

  .tile-1024, .tile-2048 {
    font-size: 16px;
  }

  /* 小屏幕方块位置 - 基于50px网格和4px间距 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 54px; }
  .tile-position-0-2 { top: 0px; left: 108px; }
  .tile-position-0-3 { top: 0px; left: 162px; }
  .tile-position-1-0 { top: 54px; left: 0px; }
  .tile-position-1-1 { top: 54px; left: 54px; }
  .tile-position-1-2 { top: 54px; left: 108px; }
  .tile-position-1-3 { top: 54px; left: 162px; }
  .tile-position-2-0 { top: 108px; left: 0px; }
  .tile-position-2-1 { top: 108px; left: 54px; }
  .tile-position-2-2 { top: 108px; left: 108px; }
  .tile-position-2-3 { top: 108px; left: 162px; }
  .tile-position-3-0 { top: 162px; left: 0px; }
  .tile-position-3-1 { top: 162px; left: 54px; }
  .tile-position-3-2 { top: 162px; left: 108px; }
  .tile-position-3-3 { top: 162px; left: 162px; }

  /* 小屏幕游戏结束弹窗 */
  .game-over-overlay {
    padding: 10px;
  }

  .game-over-message {
    padding: 15px;
    max-width: 98vw;
    max-height: 90vh;
  }

  .game-over-message h2 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .game-over-message p {
    font-size: 14px;
    margin-bottom: 15px;
  }

  .game-over-actions {
    gap: 12px;
  }

  .submit-score-btn, .restart-btn {
    padding: 12px 15px;
    font-size: 14px;
    min-height: 44px;
  }

  .submission-actions {
    gap: 10px;
  }

  .submit-btn, .cancel-btn {
    padding: 12px 15px;
    font-size: 14px;
    min-height: 44px;
  }

  .player-name-input {
    padding: 12px;
    font-size: 14px;
    min-height: 44px;
  }

  .grid-row {
    gap: 4px;
    margin-bottom: 4px;
  }

  .game-board {
    padding: var(--space-xs);
    margin-bottom: var(--space-xs);
  }
}

/* 处理高度受限的屏幕 */
@media (max-height: 600px) {
  .game-over-overlay {
    align-items: flex-start;
    padding: 10px;
    padding-top: 20px;
  }

  .game-over-message {
    padding: 15px;
    max-height: calc(100vh - 40px);
    max-width: 90vw;
  }

  .game-over-message h2 {
    font-size: 18px;
    margin-bottom: 5px;
  }

  .game-over-message p {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .game-over-actions {
    gap: 8px;
  }

  .submit-score-btn, .restart-btn {
    padding: 10px 15px;
    font-size: 14px;
    min-height: 40px;
  }

  .submission-actions {
    gap: 8px;
  }

  .submit-btn, .cancel-btn {
    padding: 10px 15px;
    font-size: 14px;
    min-height: 40px;
  }

  .player-name-input {
    padding: 10px;
    font-size: 14px;
    min-height: 40px;
  }
}

/* 处理非常小的屏幕 */
@media (max-width: 320px) {
  .game-container {
    max-width: 100%;
    width: 100%;
    padding: 4px;
    margin: 0;
    gap: 4px;
  }

  .game-over-overlay {
    padding: 5px;
  }

  .game-over-message {
    padding: 12px;
    max-width: 99vw;
    max-height: 95vh;
  }

  .game-over-message h2 {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .game-over-message p {
    font-size: 13px;
    margin-bottom: 12px;
  }

  .game-over-actions {
    gap: 10px;
  }

  .submit-score-btn, .restart-btn {
    padding: 10px 12px;
    font-size: 13px;
    min-height: 42px;
  }

  .submission-actions {
    gap: 8px;
  }

  .submit-btn, .cancel-btn {
    padding: 10px 12px;
    font-size: 13px;
    min-height: 42px;
  }

  .player-name-input {
    padding: 10px;
    font-size: 13px;
    min-height: 42px;
  }

  .grid-row {
    gap: 2px;
    margin-bottom: 2px;
  }

  .game-board {
    padding: 4px;
    margin-bottom: 4px;
  }

  .grid-cell, .tile {
    width: 45px;
    height: 45px;
    font-size: 14px;
  }

  /* 极小屏幕方块位置 - 基于45px网格和2px间距 */
  .tile-position-0-0 { top: 0px; left: 0px; }
  .tile-position-0-1 { top: 0px; left: 47px; }
  .tile-position-0-2 { top: 0px; left: 94px; }
  .tile-position-0-3 { top: 0px; left: 141px; }
  .tile-position-1-0 { top: 47px; left: 0px; }
  .tile-position-1-1 { top: 47px; left: 47px; }
  .tile-position-1-2 { top: 47px; left: 94px; }
  .tile-position-1-3 { top: 47px; left: 141px; }
  .tile-position-2-0 { top: 94px; left: 0px; }
  .tile-position-2-1 { top: 94px; left: 47px; }
  .tile-position-2-2 { top: 94px; left: 94px; }
  .tile-position-2-3 { top: 94px; left: 141px; }
  .tile-position-3-0 { top: 141px; left: 0px; }
  .tile-position-3-1 { top: 141px; left: 47px; }
  .tile-position-3-2 { top: 141px; left: 94px; }
  .tile-position-3-3 { top: 141px; left: 141px; }
}

/* {{ AURA-X: Modify - 增强触摸设备优化，添加更多触控反馈. Approval: 寸止(ID:1678886441). }} */
/* 🎯 针对触摸设备的优化 */
@media (hover: none) and (pointer: coarse) {
  .game-container {
    /* 防止双击缩放 */
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .game-board {
    /* 优化触摸响应 */
    touch-action: none;
    -webkit-tap-highlight-color: transparent;
    /* 增加触摸区域 */
    padding: var(--space-xl);
  }

  .tile {
    /* 触摸反馈优化 */
    transition: transform var(--duration-fast) var(--easing-ease);
  }

  .tile:active {
    transform: scale(0.95);
  }

  .game-over-overlay {
    position: fixed;
    z-index: 1001;
  }

  .submit-score-btn, .restart-btn, .submit-btn, .cancel-btn {
    min-height: 48px;
    min-width: 120px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    /* 增加触摸区域 */
    padding: var(--space-md) var(--space-xl);
  }

  .submit-score-btn:active, .restart-btn:active,
  .submit-btn:active, .cancel-btn:active {
    transform: scale(0.96);
    transition: transform var(--duration-fast) var(--easing-ease);
  }

  .player-name-input {
    min-height: 48px;
    touch-action: manipulation;
    /* 增加输入区域 */
    padding: var(--space-md);
    font-size: var(--font-size-lg);
  }

  /* 滑动提示 */
  .swipe-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    opacity: 0;
    transition: opacity var(--duration-normal) var(--easing-ease);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    text-align: center;
    z-index: 10;
  }

  .swipe-hint.show {
    opacity: 1;
  }

  /* 触摸反馈动画 */
  .touch-feedback {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    pointer-events: none;
    z-index: 15;
    animation: touch-ripple var(--duration-normal) var(--easing-ease);
  }

  @keyframes touch-ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }
}

/* 🎯 触控灵敏度指示器 */
.sensitivity-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  z-index: 1000;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--easing-ease);
  touch-action: manipulation;
}

.sensitivity-indicator.show {
  opacity: 1;
}

.sensitivity-indicator:active {
  transform: scale(0.9);
}

/* {{ AURA-X: Add - 可访问性增强样式，支持高对比度和减少动画. Approval: 寸止(ID:1678886417). }} */
/* 🎯 高对比度模式支持 */
.high-contrast-mode .tile {
  border: 2px solid #000000 !important;
  box-shadow: none !important;
}

.high-contrast-mode .tile-2 {
  background: #ffffff !important;
  color: #000000 !important;
}

.high-contrast-mode .tile-4 {
  background: #f0f0f0 !important;
  color: #000000 !important;
}

.high-contrast-mode .tile-8 {
  background: #e0e0e0 !important;
  color: #000000 !important;
}

.high-contrast-mode .tile-16,
.high-contrast-mode .tile-32,
.high-contrast-mode .tile-64,
.high-contrast-mode .tile-128,
.high-contrast-mode .tile-256,
.high-contrast-mode .tile-512,
.high-contrast-mode .tile-1024,
.high-contrast-mode .tile-2048 {
  background: #000000 !important;
  color: #ffffff !important;
}

.high-contrast-mode .game-board {
  background: #ffffff !important;
  border: 3px solid #000000 !important;
  box-shadow: none !important;
}

.high-contrast-mode .score-box {
  background: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
  box-shadow: none !important;
}

/* {{ AURA-X: Add - 性能优化CSS类，支持多级动画质量. Approval: 寸止(ID:1678886436). }} */
/* 🎭 减少动画模式支持 */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.reduced-motion .tile {
  transition: none !important;
}

.reduced-motion .game-container {
  animation: none !important;
}

.reduced-motion .score-box {
  animation: none !important;
}

/* 🎛️ 动画质量控制 */
/* 禁用所有动画 */
.animations-disabled * {
  animation: none !important;
  transition: none !important;
}

.animations-disabled .tile {
  transition: none !important;
}

/* 低质量模式 - 简化动画 */
.quality-low .tile {
  transition: top 0.2s ease, left 0.2s ease !important;
}

.quality-low .tile-merge,
.quality-low .tile-merge-high,
.quality-low .tile-merge-super {
  animation: tile-merge-simple 0.2s ease !important;
}

.quality-low .tile-appear-default,
.quality-low .tile-appear-spin,
.quality-low .tile-appear-drop,
.quality-low .tile-appear-burst {
  animation: tile-appear-simple 0.3s ease !important;
}

@keyframes tile-merge-simple {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes tile-appear-simple {
  0% { opacity: 0; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

/* 中等质量模式 - 标准动画 */
.quality-medium .tile {
  transition:
    top var(--duration-fast) ease,
    left var(--duration-fast) ease,
    transform var(--duration-fast) ease !important;
}

.quality-medium .tile-merge-super {
  animation: tile-merge-high var(--duration-normal) var(--easing-bounce) !important;
}

/* 高质量模式 - 完整动画（默认） */
.quality-high .tile {
  transition:
    top var(--duration-normal) var(--easing-ease),
    left var(--duration-normal) var(--easing-ease),
    transform var(--duration-fast) var(--easing-ease),
    opacity var(--duration-fast) var(--easing-ease),
    box-shadow var(--duration-fast) var(--easing-ease);
}

/* 粒子效果控制 */
.particles-disabled .merge-particle {
  display: none !important;
}

/* 背景效果控制 */
.background-effects-disabled .game-container {
  animation: none !important;
  background: var(--color-white-soft) !important;
}

.background-effects-disabled .score-box {
  animation: none !important;
}

.background-effects-disabled .tile::before,
.background-effects-disabled .tile::after {
  display: none !important;
}

/* {{ AURA-X: Modify - 增强焦点可见性和键盘导航支持. Approval: 寸止(ID:1678886444). }} */
/* 🔍 焦点可见性增强 */
.tile:focus,
.restart-btn:focus,
.submit-score-btn:focus,
.submit-btn:focus,
.cancel-btn:focus,
.game-board:focus {
  outline: 3px solid var(--color-primary) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.2) !important;
}

/* 🎹 键盘导航专用样式 */
.keyboard-navigation .game-board {
  /* 游戏板可获得焦点 */
  outline: none;
}

.keyboard-navigation .game-board:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 4px;
  box-shadow: var(--shadow-neumorphism-light), 0 0 0 4px rgba(102, 126, 234, 0.3);
}

/* 键盘操作提示 */
.keyboard-hint {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  z-index: 1000;
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--duration-normal) var(--easing-ease);
  pointer-events: none;
}

.keyboard-hint.show {
  opacity: 1;
  transform: translateY(0);
}

.keyboard-hint kbd {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: var(--font-size-xs);
  margin: 0 2px;
}

/* 跳过链接（无障碍） */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-sm);
  z-index: 1000;
  transition: top var(--duration-fast) var(--easing-ease);
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式下的焦点增强 */
@media (prefers-contrast: high) {
  .tile:focus,
  .restart-btn:focus,
  .submit-score-btn:focus,
  .submit-btn:focus,
  .cancel-btn:focus,
  .game-board:focus {
    outline: 4px solid #000000 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 8px #ffffff !important;
  }
}

/* 减少动画模式下的焦点 */
@media (prefers-reduced-motion: reduce) {
  .tile:focus,
  .restart-btn:focus,
  .submit-score-btn:focus,
  .submit-btn:focus,
  .cancel-btn:focus,
  .game-board:focus {
    transition: none !important;
  }

  .keyboard-hint {
    transition: none !important;
  }
}

/* 🎨 打印样式优化 */
@media print {
  .game-container {
    background: white !important;
    box-shadow: none !important;
  }

  .tile {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
    box-shadow: none !important;
  }

  .score-box {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
    box-shadow: none !important;
  }
}

/* {{ AURA-X: Add - 设置面板和现代按钮样式. Approval: 寸止(ID:1678886446). }} */
/* 🎛️ 设置面板样式 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
}

.settings-panel {
  background: var(--color-white-soft);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-neumorphism-float);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  animation: settings-appear var(--duration-normal) var(--easing-bounce);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-xl) var(--space-lg);
  border-bottom: 1px solid var(--color-gray-200);
}

.settings-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--color-gray-500);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--easing-ease);
}

.close-btn:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
}

.settings-content {
  padding: var(--space-lg) var(--space-xl);
}

.setting-group {
  margin-bottom: var(--space-2xl);
}

.setting-group h4 {
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
  padding: var(--space-md);
  background: var(--color-gray-50);
  border-radius: var(--radius-md);
  transition: background var(--duration-fast) var(--easing-ease);
}

.setting-item:hover {
  background: var(--color-gray-100);
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  cursor: pointer;
  flex: 1;
}

.setting-select {
  background: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-sm);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.setting-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.setting-range {
  flex: 1;
  margin: 0 var(--space-md);
  accent-color: var(--color-primary);
}

.range-value {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  min-width: 40px;
  text-align: right;
}

.performance-info {
  background: var(--color-gray-50);
  border-radius: var(--radius-md);
  padding: var(--space-md);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-sm);
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.settings-footer {
  display: flex;
  justify-content: space-between;
  padding: var(--space-lg) var(--space-xl) var(--space-xl);
  border-top: 1px solid var(--color-gray-200);
  gap: var(--space-md);
}

.reset-btn, .confirm-btn {
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  border: none;
}

.reset-btn {
  background: var(--color-gray-200);
  color: var(--color-gray-700);
}

.reset-btn:hover {
  background: var(--color-gray-300);
}

.confirm-btn {
  background: var(--color-primary);
  color: var(--color-white);
}

.confirm-btn:hover {
  background: var(--color-primary-dark);
}

/* 🎮 现代按钮样式 */
.modern-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background: linear-gradient(145deg, var(--color-white-soft) 0%, var(--color-white-mute) 100%);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  color: var(--color-gray-700);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  box-shadow: var(--shadow-neumorphism-button);
  text-decoration: none;
}

.modern-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-neumorphism-float);
  background: linear-gradient(145deg, var(--color-white) 0%, var(--color-white-soft) 100%);
}

.modern-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-neumorphism-button-pressed);
}

.btn-icon {
  font-size: var(--font-size-base);
}

.btn-text {
  font-size: var(--font-size-sm);
}

.game-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
}

.main-controls {
  display: flex;
  gap: var(--space-md);
}

.auto-save-hint {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  margin: 0;
}

.hint-icon {
  font-size: var(--font-size-sm);
}

/* 设置面板动画 */
@keyframes settings-appear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.settings-enter-active, .settings-leave-active {
  transition: all var(--duration-normal) var(--easing-ease);
}

.settings-enter-from, .settings-leave-to {
  opacity: 0;
}

.settings-enter-from .settings-panel,
.settings-leave-to .settings-panel {
  transform: scale(0.9) translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 600px) {
  .settings-panel {
    margin: var(--space-md);
    max-height: 90vh;
  }

  .settings-header,
  .settings-content,
  .settings-footer {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }

  .main-controls {
    flex-direction: column;
    width: 100%;
  }

  .modern-btn {
    justify-content: center;
    width: 100%;
  }
}
</style>
